import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider } from './src/context/ThemeContext';
import { ThemedView, ThemedText } from './src/components/common';

// Test with theme system
function TestScreen() {
  return (
    <ThemedView style={{ flex: 1, alignItems: 'center', justifyContent: 'center', padding: 20 }}>
      <ThemedText variant="title" style={{ marginBottom: 10, textAlign: 'center' }}>
        Origin Stories App
      </ThemedText>
      <ThemedText color="textSecondary" style={{ textAlign: 'center' }}>
        Testing theme system...
      </ThemedText>
      <StatusBar style="auto" />
    </ThemedView>
  );
}

export default function App() {
  return (
    <ThemeProvider>
      <TestScreen />
    </ThemeProvider>
  );
}