# Supabase Environment Variables
SUPABASE_URL=https://gvqjvkzwdwlgltiavnfg.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd2cWp2a3p3ZHdsZ2x0aWF2bmZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM0MDc1NzEsImV4cCI6MjA2ODk4MzU3MX0.OTgZpA-98XQ_Bp0hmaiZlGYosSPWW_izM-QsSa7N8tM
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# OpenAI API Key for story generation
OPENAI_API_KEY=your-openai-api-key-here

# Stripe Configuration
STRIPE_SECRET_KEY=your-stripe-secret-key-here
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret-here
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key-here

# Optional: For local development
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres