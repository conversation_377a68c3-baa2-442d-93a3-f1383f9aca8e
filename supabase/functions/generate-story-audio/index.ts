import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface GenerateAudioRequest {
  storyId: string
  voiceStyle?: 'standard' | 'premium' | 'celebrity'
  speed?: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the user from the JWT token
    const {
      data: { user },
    } = await supabaseClient.auth.getUser()

    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    const { storyId, voiceStyle = 'standard', speed = 1.0 }: GenerateAudioRequest = await req.json()

    if (!storyId) {
      return new Response(
        JSON.stringify({ error: 'Story ID is required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Get the story and verify ownership
    const { data: story, error: storyError } = await supabaseClient
      .from('stories')
      .select('*')
      .eq('id', storyId)
      .eq('user_id', user.id)
      .single()

    if (storyError || !story) {
      return new Response(
        JSON.stringify({ error: 'Story not found or access denied' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404,
        }
      )
    }

    // Check if audio already exists
    if (story.audio_file_path) {
      const { data: signedUrl } = await supabaseClient.storage
        .from('story-audio')
        .createSignedUrl(story.audio_file_path, 3600)

      return new Response(
        JSON.stringify({
          message: 'Audio already exists',
          audioUrl: signedUrl?.signedUrl,
          filePath: story.audio_file_path
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Combine story text for TTS
    const chapters = story.chapters as Array<{ title: string; text: string }>
    const fullText = [
      `Title: ${story.title}`,
      story.theme ? `Theme: ${story.theme}` : '',
      story.opening,
      ...chapters.map(chapter => `${chapter.title}. ${chapter.text}`),
      story.conclusion
    ].filter(Boolean).join('\n\n')

    // Generate audio using OpenAI TTS or similar service
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured')
    }

    // Determine voice based on subscription tier and preference
    let voice = 'alloy' // Default voice
    
    // Get user subscription info to determine available voices
    const { data: subscriptionInfo } = await supabaseClient
      .rpc('get_user_subscription_info', { user_uuid: user.id })

    if (subscriptionInfo && subscriptionInfo.length > 0) {
      const userTier = subscriptionInfo[0].tier
      
      if (voiceStyle === 'premium' && ['premium', 'pro'].includes(userTier)) {
        voice = 'nova' // Premium voice
      } else if (voiceStyle === 'celebrity' && userTier === 'pro') {
        voice = 'onyx' // Celebrity-style voice (placeholder)
      }
    }

    // Generate audio with OpenAI TTS
    const ttsResponse = await fetch('https://api.openai.com/v1/audio/speech', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'tts-1',
        input: fullText,
        voice: voice,
        speed: speed,
        response_format: 'mp3'
      }),
    })

    if (!ttsResponse.ok) {
      throw new Error('Failed to generate audio')
    }

    const audioBuffer = await ttsResponse.arrayBuffer()
    const audioFile = new Uint8Array(audioBuffer)

    // Upload to Supabase Storage
    const fileName = `${user.id}/${storyId}.mp3`
    const { data: uploadData, error: uploadError } = await supabaseClient.storage
      .from('story-audio')
      .upload(fileName, audioFile, {
        contentType: 'audio/mpeg',
        upsert: true
      })

    if (uploadError) {
      throw new Error('Failed to upload audio file: ' + uploadError.message)
    }

    // Update story record with audio file path
    const { error: updateError } = await supabaseClient
      .from('stories')
      .update({ audio_file_path: fileName })
      .eq('id', storyId)

    if (updateError) {
      throw new Error('Failed to update story record: ' + updateError.message)
    }

    // Generate signed URL for immediate access
    const { data: signedUrl } = await supabaseClient.storage
      .from('story-audio')
      .createSignedUrl(fileName, 3600)

    return new Response(
      JSON.stringify({
        message: 'Audio generated successfully',
        audioUrl: signedUrl?.signedUrl,
        filePath: fileName,
        voice: voice,
        duration: Math.ceil(fullText.length / 150) // Rough estimate: 150 chars per minute
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error generating story audio:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})