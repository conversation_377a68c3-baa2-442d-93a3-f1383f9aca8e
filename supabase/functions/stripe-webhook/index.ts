import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.9.0?target=deno'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2022-11-15',
})

const cryptoProvider = Stripe.createSubtleCryptoProvider()

serve(async (req) => {
  const signature = req.headers.get('Stripe-Signature')
  const body = await req.text()
  const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')

  if (!signature || !webhookSecret) {
    return new Response('Webhook signature verification failed', { status: 400 })
  }

  let event: Stripe.Event

  try {
    event = await stripe.webhooks.constructEventAsync(
      body,
      signature,
      webhookSecret,
      undefined,
      cryptoProvider
    )
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message)
    return new Response('Webhook signature verification failed', { status: 400 })
  }

  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        const userId = paymentIntent.metadata.user_id
        const tier = paymentIntent.metadata.subscription_tier

        if (!userId || !tier) {
          console.error('Missing user_id or subscription_tier in payment intent metadata')
          break
        }

        // Calculate subscription period (monthly)
        const now = new Date()
        const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days

        // Update or create subscription
        const { error: subscriptionError } = await supabaseClient
          .from('subscriptions')
          .upsert({
            user_id: userId,
            stripe_customer_id: paymentIntent.customer as string,
            status: 'active',
            tier,
            current_period_start: now.toISOString(),
            current_period_end: periodEnd.toISOString(),
          })

        if (subscriptionError) {
          console.error('Error updating subscription:', subscriptionError)
          break
        }

        // Update user's subscription tier and reset story count
        const { error: userError } = await supabaseClient
          .from('users')
          .update({
            subscription_tier: tier,
            subscription_expires_at: periodEnd.toISOString(),
            story_count_current_month: 0, // Reset count on new subscription
          })
          .eq('id', userId)

        if (userError) {
          console.error('Error updating user:', userError)
        }

        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        const customerId = subscription.customer as string

        // Find user by customer ID
        const { data: subscriptionData } = await supabaseClient
          .from('subscriptions')
          .select('user_id')
          .eq('stripe_customer_id', customerId)
          .single()

        if (subscriptionData) {
          // Update subscription status
          await supabaseClient
            .from('subscriptions')
            .update({ status: 'canceled' })
            .eq('stripe_customer_id', customerId)

          // Downgrade user to free tier
          await supabaseClient
            .from('users')
            .update({
              subscription_tier: 'free',
              subscription_expires_at: null,
            })
            .eq('id', subscriptionData.user_id)
        }

        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        const customerId = invoice.customer as string

        // Find user by customer ID
        const { data: subscriptionData } = await supabaseClient
          .from('subscriptions')
          .select('user_id')
          .eq('stripe_customer_id', customerId)
          .single()

        if (subscriptionData) {
          // Update subscription status to past_due
          await supabaseClient
            .from('subscriptions')
            .update({ status: 'past_due' })
            .eq('stripe_customer_id', customerId)

          // Optionally downgrade user or maintain access for grace period
          // This depends on your business logic
        }

        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response(
      JSON.stringify({ error: 'Webhook processing failed' }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})