import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface StorySettings {
  ageGroup: 'children' | 'kids' | 'teens' | 'adults' | 'professional'
  tone: 'funny' | 'inspiring' | 'epic' | 'professional' | 'standard'
}

interface GenerateStoryRequest {
  topic: string
  settings: StorySettings
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the user from the JWT token
    const {
      data: { user },
    } = await supabaseClient.auth.getUser()

    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    const { topic, settings }: GenerateStoryRequest = await req.json()

    // Validate input
    if (!topic || !settings) {
      return new Response(
        JSON.stringify({ error: 'Topic and settings are required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Check if user can generate story (usage limits)
    const { data: canGenerate, error: limitError } = await supabaseClient
      .rpc('can_generate_story', { user_uuid: user.id })

    if (limitError || !canGenerate) {
      return new Response(
        JSON.stringify({ error: 'Story generation limit reached. Please upgrade your subscription.' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 403,
        }
      )
    }

    // Generate story using OpenAI API (or your preferred AI service)
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      throw new Error('OpenAI API key not configured')
    }

    // Create age-appropriate and tone-specific prompt
    const ageGroupPrompts = {
      children: 'for children ages 3-7 with simple language and concepts',
      kids: 'for kids ages 8-12 with engaging vocabulary and relatable themes',
      teens: 'for teenagers ages 13-17 with more complex themes and modern references',
      adults: 'for adults ages 18-64 with sophisticated language and mature themes',
      professional: 'in a professional context suitable for business or educational use'
    }

    const tonePrompts = {
      funny: 'Make it humorous and entertaining with clever jokes and amusing situations.',
      inspiring: 'Make it uplifting and motivational with positive messages and hope.',
      epic: 'Make it grand and adventurous with heroic elements and dramatic moments.',
      professional: 'Keep it formal and informative with a business-appropriate tone.',
      standard: 'Use a balanced, engaging narrative style.'
    }

    const systemPrompt = `You are a master storyteller creating compelling origin stories. Create an origin story about "${topic}" ${ageGroupPrompts[settings.ageGroup]}. ${tonePrompts[settings.tone]}

Structure the story with:
1. A captivating title
2. A thematic statement
3. An engaging opening paragraph
4. 3-5 chapters with titles and content
5. A satisfying conclusion

Make the story creative, engaging, and appropriate for the specified audience. Focus on the "how it all began" aspect of the topic.`

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: `Create an origin story for: ${topic}`
          }
        ],
        max_tokens: 2000,
        temperature: 0.8,
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to generate story')
    }

    const aiResponse = await response.json()
    const storyContent = aiResponse.choices[0].message.content

    // Parse the story content (this is a simplified version - you might want more robust parsing)
    const lines = storyContent.split('\n').filter(line => line.trim())
    
    let title = topic
    let theme = ''
    let opening = ''
    let chapters: Array<{ title: string; text: string }> = []
    let conclusion = ''
    
    let currentSection = 'title'
    let currentChapter: { title: string; text: string } | null = null
    
    for (const line of lines) {
      if (line.toLowerCase().includes('title:') || line.startsWith('# ')) {
        title = line.replace(/title:/i, '').replace('#', '').trim()
      } else if (line.toLowerCase().includes('theme:')) {
        theme = line.replace(/theme:/i, '').trim()
      } else if (line.toLowerCase().includes('opening:') || currentSection === 'opening') {
        if (line.toLowerCase().includes('opening:')) {
          opening = line.replace(/opening:/i, '').trim()
          currentSection = 'chapters'
        } else if (currentSection === 'opening') {
          opening += ' ' + line
        }
      } else if (line.toLowerCase().includes('chapter') || line.startsWith('## ')) {
        if (currentChapter) {
          chapters.push(currentChapter)
        }
        currentChapter = {
          title: line.replace(/chapter \d+:/i, '').replace('##', '').trim(),
          text: ''
        }
        currentSection = 'chapter'
      } else if (line.toLowerCase().includes('conclusion:') || currentSection === 'conclusion') {
        if (line.toLowerCase().includes('conclusion:')) {
          conclusion = line.replace(/conclusion:/i, '').trim()
          currentSection = 'conclusion'
        } else if (currentSection === 'conclusion') {
          conclusion += ' ' + line
        }
      } else if (currentSection === 'chapter' && currentChapter) {
        currentChapter.text += ' ' + line
      }
    }
    
    if (currentChapter) {
      chapters.push(currentChapter)
    }

    // If parsing failed, create a simple structure
    if (!opening && !chapters.length && !conclusion) {
      opening = storyContent.substring(0, 200) + '...'
      chapters = [{ title: 'The Story', text: storyContent }]
      conclusion = 'And that is how it all began.'
    }

    // Save story to database
    const { data: savedStory, error: saveError } = await supabaseClient
      .from('stories')
      .insert({
        user_id: user.id,
        topic,
        title,
        theme,
        opening,
        chapters,
        conclusion,
        settings,
      })
      .select()
      .single()

    if (saveError) {
      throw new Error('Failed to save story: ' + saveError.message)
    }

    // Update user's story count
    await supabaseClient
      .from('users')
      .update({
        story_count_current_month: supabaseClient.sql`story_count_current_month + 1`
      })
      .eq('id', user.id)

    return new Response(
      JSON.stringify({
        story: savedStory,
        message: 'Story generated successfully'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error generating story:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})