import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ExportDocumentRequest {
  storyId: string
  format: 'pdf' | 'docx' | 'txt'
  includeAudio?: boolean
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Get the user from the JWT token
    const {
      data: { user },
    } = await supabaseClient.auth.getUser()

    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      )
    }

    const { storyId, format, includeAudio = false }: ExportDocumentRequest = await req.json()

    if (!storyId || !format) {
      return new Response(
        JSON.stringify({ error: 'Story ID and format are required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Get the story and verify ownership
    const { data: story, error: storyError } = await supabaseClient
      .from('stories')
      .select('*')
      .eq('id', storyId)
      .eq('user_id', user.id)
      .single()

    if (storyError || !story) {
      return new Response(
        JSON.stringify({ error: 'Story not found or access denied' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404,
        }
      )
    }

    // Check if document already exists
    const existingFilePath = format === 'pdf' ? story.pdf_file_path : story.docx_file_path
    if (existingFilePath && format !== 'txt') {
      const { data: signedUrl } = await supabaseClient.storage
        .from('story-documents')
        .createSignedUrl(existingFilePath, 3600)

      return new Response(
        JSON.stringify({
          message: 'Document already exists',
          documentUrl: signedUrl?.signedUrl,
          filePath: existingFilePath
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    // Prepare story content
    const chapters = story.chapters as Array<{ title: string; text: string }>
    const storyContent = {
      title: story.title,
      theme: story.theme,
      topic: story.topic,
      opening: story.opening,
      chapters: chapters,
      conclusion: story.conclusion,
      createdAt: new Date(story.created_at).toLocaleDateString(),
    }

    let documentBuffer: Uint8Array
    let contentType: string
    let fileExtension: string

    if (format === 'txt') {
      // Generate plain text
      const textContent = [
        `${storyContent.title}`,
        `Topic: ${storyContent.topic}`,
        storyContent.theme ? `Theme: ${storyContent.theme}` : '',
        `Generated on: ${storyContent.createdAt}`,
        '',
        storyContent.opening,
        '',
        ...storyContent.chapters.flatMap(chapter => [
          `${chapter.title}`,
          chapter.text,
          ''
        ]),
        storyContent.conclusion
      ].filter(Boolean).join('\n')

      documentBuffer = new TextEncoder().encode(textContent)
      contentType = 'text/plain'
      fileExtension = 'txt'
    } else if (format === 'pdf') {
      // For PDF generation, you would typically use a library like jsPDF or puppeteer
      // This is a simplified example - in production, you'd want a more robust PDF generator
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${storyContent.title}</title>
          <style>
            body { font-family: serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1 { color: #d97706; border-bottom: 2px solid #d97706; }
            h2 { color: #92400e; margin-top: 30px; }
            .metadata { color: #6b7280; font-style: italic; margin-bottom: 30px; }
            .chapter { margin-bottom: 25px; }
          </style>
        </head>
        <body>
          <h1>${storyContent.title}</h1>
          <div class="metadata">
            <p>Topic: ${storyContent.topic}</p>
            ${storyContent.theme ? `<p>Theme: ${storyContent.theme}</p>` : ''}
            <p>Generated on: ${storyContent.createdAt}</p>
          </div>
          
          <div class="opening">
            <p>${storyContent.opening}</p>
          </div>
          
          ${storyContent.chapters.map(chapter => `
            <div class="chapter">
              <h2>${chapter.title}</h2>
              <p>${chapter.text}</p>
            </div>
          `).join('')}
          
          <div class="conclusion">
            <p>${storyContent.conclusion}</p>
          </div>
        </body>
        </html>
      `

      // In a real implementation, you'd use puppeteer or similar to convert HTML to PDF
      // For now, we'll store the HTML and indicate it needs PDF conversion
      documentBuffer = new TextEncoder().encode(htmlContent)
      contentType = 'text/html' // This would be 'application/pdf' in real implementation
      fileExtension = 'html' // This would be 'pdf' in real implementation
    } else {
      // DOCX format would require a library like docx or similar
      // This is a placeholder implementation
      throw new Error('DOCX format not yet implemented')
    }

    // Upload to Supabase Storage (only for non-txt files)
    let filePath: string | null = null
    let documentUrl: string | null = null

    if (format !== 'txt') {
      const fileName = `${user.id}/${storyId}.${fileExtension}`
      const { data: uploadData, error: uploadError } = await supabaseClient.storage
        .from('story-documents')
        .upload(fileName, documentBuffer, {
          contentType: contentType,
          upsert: true
        })

      if (uploadError) {
        throw new Error('Failed to upload document: ' + uploadError.message)
      }

      filePath = fileName

      // Update story record with document file path
      const updateData = format === 'pdf' 
        ? { pdf_file_path: fileName }
        : { docx_file_path: fileName }

      const { error: updateError } = await supabaseClient
        .from('stories')
        .update(updateData)
        .eq('id', storyId)

      if (updateError) {
        throw new Error('Failed to update story record: ' + updateError.message)
      }

      // Generate signed URL for download
      const { data: signedUrl } = await supabaseClient.storage
        .from('story-documents')
        .createSignedUrl(fileName, 3600)

      documentUrl = signedUrl?.signedUrl || null
    }

    // If includeAudio is true and user has premium/pro, include audio URL
    let audioUrl: string | null = null
    if (includeAudio && story.audio_file_path) {
      const { data: signedAudioUrl } = await supabaseClient.storage
        .from('story-audio')
        .createSignedUrl(story.audio_file_path, 3600)
      
      audioUrl = signedAudioUrl?.signedUrl || null
    }

    // For txt format, return the content directly
    if (format === 'txt') {
      return new Response(documentBuffer, {
        headers: {
          ...corsHeaders,
          'Content-Type': 'text/plain',
          'Content-Disposition': `attachment; filename="${story.title.replace(/[^a-zA-Z0-9]/g, '_')}.txt"`
        },
      })
    }

    return new Response(
      JSON.stringify({
        message: 'Document generated successfully',
        documentUrl: documentUrl,
        audioUrl: audioUrl,
        filePath: filePath,
        format: format
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error exporting story document:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})