-- Create storage buckets for the Origin Stories App

-- Create bucket for story audio files (text-to-speech generated MP3s)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'story-audio',
  'story-audio',
  false,
  52428800, -- 50MB limit for audio files
  ARRAY['audio/mpeg', 'audio/mp3', 'audio/wav']
);

-- Create bucket for story documents (PDF, DOCX exports)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'story-documents',
  'story-documents',
  false,
  10485760, -- 10MB limit for documents
  ARRAY['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
);

-- Create bucket for user avatars and profile images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'user-avatars',
  'user-avatars',
  true, -- Public for easy avatar display
  2097152, -- 2MB limit for avatars
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
);

-- Create bucket for story illustrations (future feature)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'story-illustrations',
  'story-illustrations',
  false,
  5242880, -- 5MB limit for illustrations
  ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- RLS Policies for story-audio bucket
CREATE POLICY "Users can upload their own story audio" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'story-audio' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own story audio" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'story-audio' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own story audio" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'story-audio' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own story audio" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'story-audio' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- RLS Policies for story-documents bucket
CREATE POLICY "Users can upload their own story documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'story-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own story documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'story-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own story documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'story-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own story documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'story-documents' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- RLS Policies for user-avatars bucket (public bucket)
CREATE POLICY "Anyone can view avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'user-avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'user-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'user-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own avatar" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'user-avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- RLS Policies for story-illustrations bucket
CREATE POLICY "Users can upload their own story illustrations" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'story-illustrations' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own story illustrations" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'story-illustrations' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own story illustrations" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'story-illustrations' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own story illustrations" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'story-illustrations' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Add storage-related columns to stories table
ALTER TABLE public.stories ADD COLUMN audio_file_path TEXT;
ALTER TABLE public.stories ADD COLUMN pdf_file_path TEXT;
ALTER TABLE public.stories ADD COLUMN docx_file_path TEXT;
ALTER TABLE public.stories ADD COLUMN illustration_paths JSONB DEFAULT '[]';

-- Add avatar_storage_path to users table for storage-based avatars
ALTER TABLE public.users ADD COLUMN avatar_storage_path TEXT;

-- Function to clean up storage files when story is deleted
CREATE OR REPLACE FUNCTION public.cleanup_story_files()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete audio file if exists
  IF OLD.audio_file_path IS NOT NULL THEN
    PERFORM storage.delete_object('story-audio', OLD.audio_file_path);
  END IF;
  
  -- Delete PDF file if exists
  IF OLD.pdf_file_path IS NOT NULL THEN
    PERFORM storage.delete_object('story-documents', OLD.pdf_file_path);
  END IF;
  
  -- Delete DOCX file if exists
  IF OLD.docx_file_path IS NOT NULL THEN
    PERFORM storage.delete_object('story-documents', OLD.docx_file_path);
  END IF;
  
  -- Delete illustration files if they exist
  IF OLD.illustration_paths IS NOT NULL THEN
    DECLARE
      illustration_path TEXT;
    BEGIN
      FOR illustration_path IN SELECT jsonb_array_elements_text(OLD.illustration_paths)
      LOOP
        PERFORM storage.delete_object('story-illustrations', illustration_path);
      END LOOP;
    END;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to cleanup files when story is deleted
CREATE TRIGGER cleanup_story_files_trigger
  BEFORE DELETE ON public.stories
  FOR EACH ROW EXECUTE FUNCTION public.cleanup_story_files();

-- Function to clean up user avatar when user is deleted
CREATE OR REPLACE FUNCTION public.cleanup_user_avatar()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete avatar file if exists
  IF OLD.avatar_storage_path IS NOT NULL THEN
    PERFORM storage.delete_object('user-avatars', OLD.avatar_storage_path);
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to cleanup avatar when user is deleted
CREATE TRIGGER cleanup_user_avatar_trigger
  BEFORE DELETE ON public.users
  FOR EACH ROW EXECUTE FUNCTION public.cleanup_user_avatar();

-- Helper function to get signed URL for private files
CREATE OR REPLACE FUNCTION public.get_story_file_url(
  bucket_name TEXT,
  file_path TEXT,
  expires_in INTEGER DEFAULT 3600
)
RETURNS TEXT AS $$
BEGIN
  -- Only allow access to user's own files
  IF NOT (auth.uid()::text = (storage.foldername(file_path))[1]) THEN
    RETURN NULL;
  END IF;
  
  RETURN storage.get_signed_url(bucket_name, file_path, expires_in);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;