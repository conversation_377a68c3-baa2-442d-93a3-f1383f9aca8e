-- Function to reset monthly story counts (to be called monthly via cron)
CREATE OR REPLACE FUNCTION public.reset_monthly_story_counts()
RETURNS void AS $$
BEGIN
  UPDATE public.users 
  SET story_count_current_month = 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's remaining stories for current month
CREATE OR REPLACE FUNCTION public.get_remaining_stories(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  current_count INTEGER;
  story_limit INTEGER;
BEGIN
  SELECT story_count_current_month INTO current_count
  FROM public.users
  WHERE id = user_uuid;
  
  SELECT public.get_user_story_limit(user_uuid) INTO story_limit;
  
  RETURN GREATEST(0, story_limit - current_count);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check subscription status
CREATE OR REPLACE FUNCTION public.is_subscription_active(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  expires_at TIMESTAMP WITH TIME ZONE;
  sub_tier TEXT;
BEGIN
  SELECT subscription_tier, subscription_expires_at 
  INTO sub_tier, expires_at
  FROM public.users
  WHERE id = user_uuid;
  
  -- Free tier is always "active"
  IF sub_tier = 'free' THEN
    RETURN TRUE;
  END IF;
  
  -- Check if subscription hasn't expired
  RETURN expires_at IS NULL OR expires_at > NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's subscription info
CREATE OR REPLACE FUNCTION public.get_user_subscription_info(user_uuid UUID)
RETURNS TABLE(
  tier TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  stories_used INTEGER,
  stories_limit INTEGER,
  stories_remaining INTEGER,
  is_active BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.subscription_tier,
    u.subscription_expires_at,
    u.story_count_current_month,
    public.get_user_story_limit(user_uuid),
    public.get_remaining_stories(user_uuid),
    public.is_subscription_active(user_uuid)
  FROM public.users u
  WHERE u.id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a view for user stories with metadata
CREATE OR REPLACE VIEW public.user_stories_view AS
SELECT 
  s.id,
  s.user_id,
  s.topic,
  s.title,
  s.theme,
  s.opening,
  s.chapters,
  s.conclusion,
  s.settings,
  s.created_at,
  s.updated_at,
  LENGTH(s.opening || s.conclusion || COALESCE(s.chapters::text, '')) as estimated_word_count
FROM public.stories s;

-- Grant access to the view
GRANT SELECT ON public.user_stories_view TO authenticated;

-- Note: Views inherit RLS policies from their underlying tables
-- The user_stories_view will automatically respect the RLS policies on the stories table

-- Function to delete user account and all associated data
CREATE OR REPLACE FUNCTION public.delete_user_account(user_uuid UUID)
RETURNS void AS $$
BEGIN
  -- Only allow users to delete their own account
  IF auth.uid() != user_uuid THEN
    RAISE EXCEPTION 'Unauthorized: Cannot delete another user''s account';
  END IF;
  
  -- Delete in order to respect foreign key constraints
  DELETE FROM public.subscriptions WHERE user_id = user_uuid;
  DELETE FROM public.stories WHERE user_id = user_uuid;
  DELETE FROM public.users WHERE id = user_uuid;
  
  -- Note: This will also trigger deletion from auth.users due to CASCADE
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_subscription_tier ON public.users(subscription_tier);
CREATE INDEX IF NOT EXISTS idx_users_subscription_expires_at ON public.users(subscription_expires_at);
CREATE INDEX IF NOT EXISTS idx_stories_topic ON public.stories(topic);
CREATE INDEX IF NOT EXISTS idx_stories_title ON public.stories(title);

-- Create a function to search stories by topic or title
CREATE OR REPLACE FUNCTION public.search_user_stories(
  user_uuid UUID,
  search_term TEXT
)
RETURNS TABLE(
  id UUID,
  topic TEXT,
  title TEXT,
  theme TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id,
    s.topic,
    s.title,
    s.theme,
    s.created_at
  FROM public.stories s
  WHERE s.user_id = user_uuid
    AND (
      s.topic ILIKE '%' || search_term || '%' OR
      s.title ILIKE '%' || search_term || '%' OR
      s.theme ILIKE '%' || search_term || '%'
    )
  ORDER BY s.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;