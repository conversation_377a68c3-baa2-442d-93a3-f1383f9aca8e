# Supabase Backend Infrastructure Setup

This document provides complete setup instructions for the Origin Stories App Supabase backend infrastructure.

## Prerequisites

1. Install Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Create a Supabase account at https://supabase.com
3. Install Node.js and npm
4. Have your OpenAI API key ready
5. Have your Stripe account set up

## Setup Steps

### 1. Initialize Local Development

```bash
# Navigate to project root
cd origin-stories-app

# Initialize Supabase (if not already done)
supabase init

# Start local Supabase services
supabase start
```

### 2. Create Remote Supabase Project

1. Go to https://supabase.com/dashboard
2. Click "New Project"
3. Choose organization and enter project details:
   - Name: "Origin Stories App"
   - Database Password: (generate a secure password)
   - Region: Choose closest to your users

### 3. Link Local Project to Remote

```bash
# Link to your remote project
supabase link --project-ref YOUR_PROJECT_REF

# Get your project ref from the Supabase dashboard URL:
# https://supabase.com/dashboard/project/YOUR_PROJECT_REF
```

### 4. Apply Database Migrations

```bash
# Apply all migrations to remote database
supabase db push

# Or apply them one by one:
supabase migration up
```

**Note:** Make sure to run all 4 migration files in order:
1. `20240101000001_initial_schema.sql` - Creates basic tables
2. `20240101000002_rls_policies.sql` - Sets up security policies
3. `20240101000003_additional_functions.sql` - Adds utility functions
4. `20240101000004_storage_setup.sql` - Configures storage buckets and file handling

### 5. Configure Authentication Providers

In your Supabase dashboard:

1. Go to Authentication > Providers
2. Configure Email provider (enabled by default)
3. Configure Google OAuth:
   - Enable Google provider
   - Add your Google OAuth client ID and secret
   - Set redirect URL: `https://YOUR_PROJECT_REF.supabase.co/auth/v1/callback`
4. Configure Apple OAuth:
   - Enable Apple provider
   - Add your Apple OAuth credentials
   - Set redirect URL: `https://YOUR_PROJECT_REF.supabase.co/auth/v1/callback`

### 6. Deploy Edge Functions

```bash
# Deploy all edge functions
supabase functions deploy

# Or deploy individually:
supabase functions deploy create-payment-intent
supabase functions deploy generate-story
supabase functions deploy stripe-webhook
supabase functions deploy generate-story-audio
supabase functions deploy export-story-document
```

### 7. Set Environment Variables

In your Supabase dashboard, go to Settings > Edge Functions and add:

```
OPENAI_API_KEY=your_openai_api_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here
```

### 8. Configure Stripe Webhooks

1. In your Stripe dashboard, go to Developers > Webhooks
2. Add endpoint: `https://YOUR_PROJECT_REF.supabase.co/functions/v1/stripe-webhook`
3. Select events to listen for:
   - `payment_intent.succeeded`
   - `customer.subscription.deleted`
   - `invoice.payment_failed`

### 9. Get Project Credentials

After setup, you'll need these for your app:

```bash
# Get project URL
supabase status

# Get anon key (for client-side)
# Go to Settings > API in Supabase dashboard

# Get service role key (for server-side, keep secret)
# Go to Settings > API in Supabase dashboard
```

## Environment Variables for App

Create a `.env` file in your app with:

```
EXPO_PUBLIC_SUPABASE_URL=https://YOUR_PROJECT_REF.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

## Testing the Setup

1. Check if tables were created:
   ```bash
   supabase db diff
   ```

2. Test Edge Functions locally:
   ```bash
   supabase functions serve
   ```

3. Test authentication flow in your app

## Troubleshooting

- If migrations fail, check the logs: `supabase logs`
- If Edge Functions fail, check function logs in Supabase dashboard
- Ensure all environment variables are set correctly
- Check that RLS policies are working by testing with different users