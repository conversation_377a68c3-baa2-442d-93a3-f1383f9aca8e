import { useAuth } from '../context/AuthContext';
import { supabaseHelpers } from '../services/supabase';

/**
 * Custom hook that provides authentication helpers and utilities
 */
export function useAuthHelpers() {
  const auth = useAuth();

  /**
   * Check if user has a specific subscription tier or higher
   */
  const hasSubscriptionTier = (requiredTier: 'plus' | 'premium' | 'pro'): boolean => {
    if (!auth.user) return false;

    const tierHierarchy = {
      free: 0,
      plus: 1,
      premium: 2,
      pro: 3,
    };

    const userTierLevel = tierHierarchy[auth.user.subscription_tier];
    const requiredTierLevel = tierHierarchy[requiredTier];

    return userTierLevel >= requiredTierLevel;
  };

  /**
   * Check if user can generate a story based on their subscription limits
   */
  const canGenerateStory = async (): Promise<boolean> => {
    if (!auth.user) return false;

    try {
      return await supabaseHelpers.canGenerateStory(auth.user.id);
    } catch (error) {
      console.error('Error checking story generation limit:', error);
      return false;
    }
  };

  /**
   * Get user's subscription information
   */
  const getSubscriptionInfo = async () => {
    if (!auth.user) return null;

    try {
      return await supabaseHelpers.getUserSubscriptionInfo(auth.user.id);
    } catch (error) {
      console.error('Error getting subscription info:', error);
      return null;
    }
  };

  /**
   * Check if user's subscription is active
   */
  const isSubscriptionActive = (): boolean => {
    if (!auth.user) return false;

    // Free tier is always "active"
    if (auth.user.subscription_tier === 'free') return true;

    // Check if subscription hasn't expired
    if (auth.user.subscription_expires_at) {
      const expirationDate = new Date(auth.user.subscription_expires_at);
      return expirationDate > new Date();
    }

    return false;
  };

  /**
   * Get remaining stories for current billing period
   */
  const getRemainingStories = async (): Promise<number> => {
    if (!auth.user) return 0;

    try {
      const subscriptionInfo = await getSubscriptionInfo();
      return subscriptionInfo?.stories_remaining || 0;
    } catch (error) {
      console.error('Error getting remaining stories:', error);
      return 0;
    }
  };

  /**
   * Update user profile
   */
  const updateProfile = async (updates: {
    full_name?: string;
    avatar_url?: string;
    settings?: any;
  }) => {
    if (!auth.user) throw new Error('User not authenticated');

    try {
      const updatedUser = await supabaseHelpers.updateUserProfile(auth.user.id, updates);
      
      // Refresh the auth context to get updated user data
      await auth.refreshSession();
      
      return updatedUser;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  };

  /**
   * Delete user account
   */
  const deleteAccount = async () => {
    if (!auth.user) throw new Error('User not authenticated');

    try {
      await supabaseHelpers.deleteUserAccount(auth.user.id);
      await auth.signOut();
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  };

  /**
   * Check if user has completed onboarding
   */
  const hasCompletedOnboarding = (): boolean => {
    if (!auth.user) return false;
    
    // Check if user has set up basic preferences
    const settings = auth.user.settings as any;
    return !!(
      settings?.defaultAgeGroup &&
      settings?.defaultTone !== undefined
    );
  };

  /**
   * Get user's display name
   */
  const getDisplayName = (): string => {
    if (!auth.user) return 'Guest';
    
    return auth.user.full_name || 
           auth.user.email.split('@')[0] || 
           'User';
  };

  /**
   * Check if user email is verified
   */
  const isEmailVerified = (): boolean => {
    return !!auth.session?.user?.email_confirmed_at;
  };

  /**
   * Get user's avatar URL or initials
   */
  const getAvatarInfo = (): { url?: string; initials: string } => {
    if (!auth.user) return { initials: 'G' };

    const initials = auth.user.full_name
      ? auth.user.full_name
          .split(' ')
          .map(name => name.charAt(0))
          .join('')
          .toUpperCase()
          .slice(0, 2)
      : auth.user.email.charAt(0).toUpperCase();

    return {
      url: auth.user.avatar_url || undefined,
      initials,
    };
  };

  return {
    ...auth,
    hasSubscriptionTier,
    canGenerateStory,
    getSubscriptionInfo,
    isSubscriptionActive,
    getRemainingStories,
    updateProfile,
    deleteAccount,
    hasCompletedOnboarding,
    getDisplayName,
    isEmailVerified,
    getAvatarInfo,
  };
}