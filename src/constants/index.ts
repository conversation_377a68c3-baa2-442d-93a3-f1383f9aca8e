// App constants and configuration

export const APP_CONFIG = {
  name: 'Origin Stories',
  version: '1.0.0',
  description: 'Generate compelling origin stories for any topic',
};

export const SUBSCRIPTION_TIERS = {
  FREE: {
    name: 'Free',
    price: 0,
    storiesPerMonth: 5,
    features: ['Basic story generation', 'Story history', 'Text export'],
  },
  PLUS: {
    name: 'Plus',
    price: 3.99,
    storiesPerMonth: 30,
    features: ['All Free features', 'PDF export', 'Basic text-to-speech'],
  },
  PREMIUM: {
    name: 'Premium',
    price: 9.99,
    storiesPerMonth: 100,
    features: ['All Plus features', 'Voice style packs', 'DOCX export', 'Priority support'],
  },
  PRO: {
    name: 'Pro',
    price: 29.99,
    storiesPerMonth: 300,
    features: ['All Premium features', 'Celebrity-style voices', 'PDF + MP3 combo', 'Advanced customization'],
  },
};

export const STORY_SETTINGS = {
  AGE_GROUPS: [
    { value: 'children', label: 'Children (3-7)', description: 'Simple language, colorful imagery' },
    { value: 'kids', label: 'Kids (8-12)', description: 'Adventure-focused, age-appropriate complexity' },
    { value: 'teens', label: 'Teens (13-17)', description: 'Coming-of-age themes, relatable characters' },
    { value: 'adults', label: 'Adults (18-64)', description: 'Sophisticated narratives, complex themes' },
    { value: 'professional', label: 'Professional', description: 'Business-appropriate, inspirational' },
  ],
  TONES: [
    { value: 'standard', label: 'Standard', description: 'Balanced and engaging' },
    { value: 'funny', label: 'Funny', description: 'Humorous and lighthearted' },
    { value: 'inspiring', label: 'Inspiring', description: 'Motivational and uplifting' },
    { value: 'epic', label: 'Epic', description: 'Grand and dramatic' },
    { value: 'professional', label: 'Professional', description: 'Formal and business-focused' },
  ],
};

export const COLORS = {
  LIGHT: {
    primary: '#92400e', // amber-800
    secondary: '#b45309', // amber-700
    background: '#fffbeb', // amber-50
    surface: '#ffffff',
    text: '#1f2937', // gray-800
    textSecondary: '#6b7280', // gray-500
    border: '#e5e7eb', // gray-200
    error: '#dc2626', // red-600
    success: '#059669', // emerald-600
  },
  DARK: {
    primary: '#fbbf24', // amber-400
    secondary: '#f59e0b', // amber-500
    background: '#111827', // gray-900
    surface: '#1f2937', // gray-800
    text: '#f9fafb', // gray-50
    textSecondary: '#d1d5db', // gray-300
    border: '#374151', // gray-700
    error: '#f87171', // red-400
    success: '#34d399', // emerald-400
  },
};

export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  serif: 'Georgia',
};