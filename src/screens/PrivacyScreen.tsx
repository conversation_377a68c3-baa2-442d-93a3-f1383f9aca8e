import React from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { ThemedView, ThemedText } from '../components/common';

export default function PrivacyScreen() {
  const { colors } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <ThemedView backgroundColor="card" style={styles.contentCard}>
          <ThemedText variant="title" style={styles.title}>
            Privacy Policy
          </ThemedText>
          
          <ThemedText color="textSecondary" style={styles.lastUpdated}>
            Last updated: January 2024
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Information We Collect
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            We collect information you provide directly to us, such as when you create an account, generate stories, or contact us for support.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            How We Use Your Information
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Information Sharing
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Data Security
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Data Retention
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            We retain your information for as long as your account is active or as needed to provide you services and comply with legal obligations.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Your Rights
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            You have the right to access, update, or delete your personal information. You may also request that we restrict or stop processing your data.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Cookies and Tracking
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            We use cookies and similar technologies to enhance your experience, analyze usage patterns, and improve our services.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Changes to This Policy
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page.
          </ThemedText>

          <ThemedView style={styles.contactSection}>
            <ThemedText variant="subtitle" style={styles.contactTitle}>
              Contact Us
            </ThemedText>
            <ThemedText color="textSecondary" style={styles.contactText}>
              If you have any questions about this Privacy Policy, please contact us at:
            </ThemedText>
            <ThemedText color="primary" style={styles.contactEmail}>
              <EMAIL>
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  contentCard: {
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 28,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    marginTop: 24,
    marginBottom: 12,
  },
  sectionText: {
    fontSize: 16,
    fontFamily: 'serif',
    lineHeight: 24,
    marginBottom: 16,
  },
  contactSection: {
    marginTop: 32,
    padding: 20,
    borderRadius: 8,
    backgroundColor: '#f9fafb',
  },
  contactTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    marginBottom: 8,
  },
  contactText: {
    fontSize: 14,
    fontFamily: 'serif',
    lineHeight: 20,
    marginBottom: 8,
  },
  contactEmail: {
    fontSize: 16,
    fontFamily: 'serif',
    fontWeight: 'bold',
  },
});
