import React from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../context/ThemeContext';
import { ThemedView, ThemedText } from '../components/common';

export default function TermsScreen() {
  const { colors } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <ThemedView backgroundColor="card" style={styles.contentCard}>
          <ThemedText variant="title" style={styles.title}>
            Terms of Use
          </ThemedText>
          
          <ThemedText color="textSecondary" style={styles.lastUpdated}>
            Last updated: January 2024
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            1. Acceptance of Terms
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            By accessing and using the Origin Stories app, you accept and agree to be bound by the terms and provision of this agreement.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            2. Use License
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            Permission is granted to temporarily use the Origin Stories app for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            3. Content Generation
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            Stories generated through our AI service are provided for entertainment and educational purposes. While we strive for accuracy, the content should not be considered factual historical information.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            4. User Accounts
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            5. Subscription Terms
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            Subscription fees are billed in advance on a monthly basis. You may cancel your subscription at any time through your account settings.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            6. Privacy
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the service.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            7. Limitations
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            In no event shall Origin Stories or its suppliers be liable for any damages arising out of the use or inability to use the materials on the app.
          </ThemedText>

          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            8. Modifications
          </ThemedText>
          <ThemedText style={styles.sectionText}>
            Origin Stories may revise these terms of use at any time without notice. By using this app, you are agreeing to be bound by the current version of these terms.
          </ThemedText>

          <ThemedView style={styles.contactSection}>
            <ThemedText variant="subtitle" style={styles.contactTitle}>
              Contact Information
            </ThemedText>
            <ThemedText color="textSecondary" style={styles.contactText}>
              If you have any questions about these Terms of Use, please contact us at:
            </ThemedText>
            <ThemedText color="primary" style={styles.contactEmail}>
              <EMAIL>
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  contentCard: {
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 28,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    marginTop: 24,
    marginBottom: 12,
  },
  sectionText: {
    fontSize: 16,
    fontFamily: 'serif',
    lineHeight: 24,
    marginBottom: 16,
  },
  contactSection: {
    marginTop: 32,
    padding: 20,
    borderRadius: 8,
    backgroundColor: '#f9fafb',
  },
  contactTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    marginBottom: 8,
  },
  contactText: {
    fontSize: 14,
    fontFamily: 'serif',
    lineHeight: 20,
    marginBottom: 8,
  },
  contactEmail: {
    fontSize: 16,
    fontFamily: 'serif',
    fontWeight: 'bold',
  },
});
