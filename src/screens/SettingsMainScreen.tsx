import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { 
  User, 
  CreditCard, 
  FileText, 
  Shield, 
  LogOut, 
  ChevronRight,
  Crown
} from 'lucide-react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuthHelpers } from '../hooks/useAuthHelpers';
import { ThemedView, ThemedText, ThemeToggle } from '../components/common';
import { SettingsStackParamList } from '../types/navigation';

type SettingsNavigationProp = StackNavigationProp<SettingsStackParamList, 'SettingsMain'>;

export default function SettingsMainScreen() {
  const navigation = useNavigation<SettingsNavigationProp>();
  const { colors } = useTheme();
  const { user, signOut, getDisplayName } = useAuthHelpers();

  const settingsItems = [
    {
      id: 'profile',
      title: 'Profile',
      subtitle: 'Manage your account information',
      icon: User,
      onPress: () => navigation.navigate('Profile'),
      available: true,
    },
    {
      id: 'subscription',
      title: 'Subscription',
      subtitle: `Current plan: ${user?.subscription_tier || 'Free'}`,
      icon: Crown,
      onPress: () => navigation.navigate('Subscription'),
      available: true,
    },
    {
      id: 'terms',
      title: 'Terms of Use',
      subtitle: 'Read our terms and conditions',
      icon: FileText,
      onPress: () => navigation.navigate('Terms'),
      available: true,
    },
    {
      id: 'privacy',
      title: 'Privacy Policy',
      subtitle: 'Learn about data protection',
      icon: Shield,
      onPress: () => navigation.navigate('Privacy'),
      available: true,
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* User Profile Section */}
        <ThemedView backgroundColor="card" style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <User size={32} color={colors.textSecondary} />
            </View>
            <View style={styles.profileInfo}>
              <ThemedText variant="subtitle" style={styles.userName}>
                {getDisplayName()}
              </ThemedText>
              <ThemedText color="textSecondary" style={styles.userEmail}>
                {user?.email}
              </ThemedText>
              <View style={styles.subscriptionBadge}>
                <Crown size={14} color={colors.accent} />
                <ThemedText color="textSecondary" style={styles.subscriptionText}>
                  {user?.subscription_tier || 'Free'} Plan
                </ThemedText>
              </View>
            </View>
            <View style={styles.themeToggleContainer}>
              <ThemeToggle />
            </View>
          </View>
        </ThemedView>

        {/* Usage Stats */}
        <ThemedView backgroundColor="card" style={styles.statsCard}>
          <ThemedText variant="subtitle" style={styles.statsTitle}>
            This Month
          </ThemedText>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <ThemedText variant="title" color="primary" style={styles.statNumber}>
                {user?.story_count_current_month || 0}
              </ThemedText>
              <ThemedText color="textSecondary" style={styles.statLabel}>
                Stories Created
              </ThemedText>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <ThemedText variant="title" color="primary" style={styles.statNumber}>
                {user?.subscription_tier === 'free' ? '5' : 
                 user?.subscription_tier === 'plus' ? '30' :
                 user?.subscription_tier === 'premium' ? '100' : '300'}
              </ThemedText>
              <ThemedText color="textSecondary" style={styles.statLabel}>
                Monthly Limit
              </ThemedText>
            </View>
          </View>
        </ThemedView>

        {/* Settings Items */}
        <ThemedView backgroundColor="card" style={styles.settingsCard}>
          {settingsItems.map((item, index) => (
            <TouchableOpacity
              key={item.id}
              style={[
                styles.settingsItem,
                index < settingsItems.length - 1 && styles.settingsItemBorder
              ]}
              onPress={item.onPress}
              disabled={!item.available}
            >
              <View style={styles.settingsItemLeft}>
                <View style={styles.settingsItemIcon}>
                  <item.icon size={20} color={colors.textSecondary} />
                </View>
                <View style={styles.settingsItemText}>
                  <ThemedText style={styles.settingsItemTitle}>
                    {item.title}
                  </ThemedText>
                  <ThemedText color="textSecondary" style={styles.settingsItemSubtitle}>
                    {item.subtitle}
                  </ThemedText>
                </View>
              </View>
              <ChevronRight size={20} color={colors.textMuted} />
            </TouchableOpacity>
          ))}
        </ThemedView>

        {/* Sign Out Button */}
        <TouchableOpacity
          style={[styles.signOutButton, { borderColor: colors.error }]}
          onPress={signOut}
        >
          <LogOut size={20} color={colors.error} />
          <ThemedText style={[styles.signOutText, { color: colors.error }]}>
            Sign Out
          </ThemedText>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  profileCard: {
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontFamily: 'serif',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    fontFamily: 'serif',
    marginBottom: 8,
  },
  subscriptionBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  subscriptionText: {
    fontSize: 12,
    fontFamily: 'serif',
  },
  themeToggleContainer: {
    marginLeft: 16,
  },
  statsCard: {
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsTitle: {
    fontSize: 16,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontFamily: 'serif',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'serif',
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#e5e7eb',
    marginHorizontal: 16,
  },
  settingsCard: {
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
  },
  settingsItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  settingsItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingsItemIcon: {
    marginRight: 16,
  },
  settingsItemText: {
    flex: 1,
  },
  settingsItemTitle: {
    fontSize: 16,
    fontFamily: 'serif',
    marginBottom: 2,
  },
  settingsItemSubtitle: {
    fontSize: 12,
    fontFamily: 'serif',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  signOutText: {
    fontSize: 16,
    fontFamily: 'serif',
  },
});
