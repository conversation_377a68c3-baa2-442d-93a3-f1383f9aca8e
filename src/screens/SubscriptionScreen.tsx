import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Crown, Check, Star } from 'lucide-react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuthHelpers } from '../hooks/useAuthHelpers';
import { ThemedView, ThemedText } from '../components/common';

export default function SubscriptionScreen() {
  const { colors } = useTheme();
  const { user } = useAuthHelpers();

  const subscriptionPlans = [
    {
      id: 'free',
      name: 'Free',
      price: '$0',
      period: 'forever',
      storiesLimit: 5,
      features: [
        '5 stories per month',
        'Basic story generation',
        'Text export',
        'Community support',
      ],
      current: user?.subscription_tier === 'free',
      popular: false,
    },
    {
      id: 'plus',
      name: 'Plus',
      price: '$3.99',
      period: 'month',
      storiesLimit: 30,
      features: [
        '30 stories per month',
        'Enhanced story quality',
        'PDF & DOCX export',
        'Basic text-to-speech',
        'Email support',
      ],
      current: user?.subscription_tier === 'plus',
      popular: false,
    },
    {
      id: 'premium',
      name: 'Premium',
      price: '$9.99',
      period: 'month',
      storiesLimit: 100,
      features: [
        '100 stories per month',
        'Premium story quality',
        'All export formats',
        'Premium voice styles',
        'Story customization',
        'Priority support',
      ],
      current: user?.subscription_tier === 'premium',
      popular: true,
    },
    {
      id: 'pro',
      name: 'Pro',
      price: '$29.99',
      period: 'month',
      storiesLimit: 300,
      features: [
        '300 stories per month',
        'Professional quality',
        'All premium features',
        'Celebrity voice styles',
        'Advanced customization',
        'Dedicated support',
        'Early access to features',
      ],
      current: user?.subscription_tier === 'pro',
      popular: false,
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Current Plan */}
        <ThemedView backgroundColor="card" style={styles.currentPlanCard}>
          <View style={styles.currentPlanHeader}>
            <Crown size={24} color={colors.accent} />
            <ThemedText variant="subtitle" style={styles.currentPlanTitle}>
              Current Plan
            </ThemedText>
          </View>
          <ThemedText variant="title" style={styles.currentPlanName}>
            {user?.subscription_tier || 'Free'} Plan
          </ThemedText>
          <ThemedText color="textSecondary" style={styles.currentPlanUsage}>
            {user?.story_count_current_month || 0} stories used this month
          </ThemedText>
        </ThemedView>

        {/* Subscription Plans */}
        <ThemedText variant="subtitle" style={styles.plansTitle}>
          Choose Your Plan
        </ThemedText>

        {subscriptionPlans.map((plan) => (
          <ThemedView 
            key={plan.id} 
            backgroundColor="card" 
            style={[
              styles.planCard,
              plan.current && styles.currentPlan,
              plan.popular && styles.popularPlan,
            ]}
          >
            {plan.popular && (
              <View style={styles.popularBadge}>
                <Star size={16} color="white" />
                <ThemedText style={styles.popularText}>Most Popular</ThemedText>
              </View>
            )}
            
            <View style={styles.planHeader}>
              <ThemedText variant="subtitle" style={styles.planName}>
                {plan.name}
              </ThemedText>
              <View style={styles.planPricing}>
                <ThemedText variant="title" style={styles.planPrice}>
                  {plan.price}
                </ThemedText>
                <ThemedText color="textSecondary" style={styles.planPeriod}>
                  /{plan.period}
                </ThemedText>
              </View>
            </View>

            <ThemedText color="textSecondary" style={styles.planStoriesLimit}>
              {plan.storiesLimit} stories per month
            </ThemedText>

            <View style={styles.planFeatures}>
              {plan.features.map((feature, index) => (
                <View key={index} style={styles.featureRow}>
                  <Check size={16} color={colors.success} />
                  <ThemedText style={styles.featureText}>
                    {feature}
                  </ThemedText>
                </View>
              ))}
            </View>

            <ThemedView style={styles.planButtonContainer}>
              {plan.current ? (
                <ThemedView style={styles.currentPlanButton}>
                  <ThemedText style={styles.currentPlanButtonText}>
                    Current Plan
                  </ThemedText>
                </ThemedView>
              ) : (
                <ThemedView style={styles.upgradePlaceholder}>
                  <ThemedText color="textMuted" style={styles.upgradePlaceholderText}>
                    Upgrade functionality will be implemented in Task 11
                  </ThemedText>
                </ThemedView>
              )}
            </ThemedView>
          </ThemedView>
        ))}

        {/* Billing Info */}
        <ThemedView backgroundColor="card" style={styles.billingCard}>
          <ThemedText variant="subtitle" style={styles.billingTitle}>
            Billing Information
          </ThemedText>
          <ThemedText color="textSecondary" style={styles.billingDescription}>
            Subscription management and billing will be implemented in Task 11 with Stripe integration.
          </ThemedText>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  currentPlanCard: {
    borderRadius: 12,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  currentPlanHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  currentPlanTitle: {
    fontSize: 16,
    fontFamily: 'serif',
  },
  currentPlanName: {
    fontSize: 24,
    fontFamily: 'serif',
    marginBottom: 8,
  },
  currentPlanUsage: {
    fontSize: 14,
    fontFamily: 'serif',
  },
  plansTitle: {
    fontSize: 20,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 24,
  },
  planCard: {
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  currentPlan: {
    borderWidth: 2,
    borderColor: '#10b981',
  },
  popularPlan: {
    borderWidth: 2,
    borderColor: '#f59e0b',
  },
  popularBadge: {
    position: 'absolute',
    top: -12,
    left: 24,
    right: 24,
    backgroundColor: '#f59e0b',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    zIndex: 1,
  },
  popularText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'serif',
    fontWeight: 'bold',
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 8,
  },
  planName: {
    fontSize: 20,
    fontFamily: 'serif',
  },
  planPricing: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  planPrice: {
    fontSize: 24,
    fontFamily: 'serif',
  },
  planPeriod: {
    fontSize: 14,
    fontFamily: 'serif',
  },
  planStoriesLimit: {
    fontSize: 14,
    fontFamily: 'serif',
    marginBottom: 16,
  },
  planFeatures: {
    gap: 8,
    marginBottom: 24,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    fontFamily: 'serif',
    flex: 1,
  },
  planButtonContainer: {
    alignItems: 'center',
  },
  currentPlanButton: {
    backgroundColor: '#10b981',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  currentPlanButtonText: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'serif',
    fontWeight: 'bold',
  },
  upgradePlaceholder: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
  },
  upgradePlaceholderText: {
    fontSize: 12,
    fontFamily: 'serif',
    textAlign: 'center',
  },
  billingCard: {
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  billingTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    marginBottom: 12,
  },
  billingDescription: {
    fontSize: 14,
    fontFamily: 'serif',
    lineHeight: 20,
  },
});
