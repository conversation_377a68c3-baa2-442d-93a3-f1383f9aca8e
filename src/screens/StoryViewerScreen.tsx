import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { RouteProp } from '@react-navigation/native';
import { Book, Play, Share, Download } from 'lucide-react-native';
import { useTheme } from '../context/ThemeContext';
import { ThemedView, ThemedText } from '../components/common';
import { HistoryStackParamList } from '../types/navigation';

type StoryViewerScreenRouteProp = RouteProp<HistoryStackParamList, 'StoryViewer'>;

interface Props {
  route: StoryViewerScreenRouteProp;
}

export default function StoryViewerScreen({ route }: Props) {
  const { storyId } = route.params;
  const { colors } = useTheme();

  // Placeholder story data - will be replaced with real data from Supabase in Task 7
  const placeholderStory = {
    id: storyId,
    title: 'The Origin of Coffee',
    theme: 'A tale of discovery and awakening',
    topic: 'Coffee',
    opening: 'In the ancient highlands of Ethiopia, where the morning mist dances through emerald leaves and the air carries whispers of forgotten legends, there lived a young goat herder named <PERSON><PERSON><PERSON>. His days were simple, his world small, yet destiny had chosen him to unlock one of humanity\'s most cherished secrets.',
    chapters: [
      {
        title: 'The Dancing Goats',
        text: '<PERSON>ldi noticed his goats behaving strangely after eating certain red berries from a mysterious shrub. They danced with unusual energy, their eyes bright with an otherworldly vigor that lasted well into the night.',
      },
      {
        title: 'The Monastery\'s Discovery',
        text: 'Intrigued, Kaldi brought the berries to the local monastery. The monks, initially skeptical, soon discovered that brewing these berries created a drink that kept them alert during long prayers and meditation.',
      },
      {
        title: 'The Spread of Wonder',
        text: 'Word of this miraculous beverage spread across the ancient trade routes. From Ethiopia to Yemen, from the Ottoman Empire to Venice, coffee began its journey to awaken the world.',
      },
    ],
    conclusion: 'And so, from a simple observation by a humble goat herder, coffee was born. Today, billions of people around the world begin their day with this ancient gift, each cup carrying within it the spirit of discovery and the promise of awakening that Kaldi first witnessed in his dancing goats.',
    createdAt: '2024-01-15',
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Book Cover */}
        <ThemedView style={[styles.bookCover, { backgroundColor: colors.accent }]}>
          <ThemedText style={styles.bookTitle}>
            {placeholderStory.title}
          </ThemedText>
          <ThemedText style={styles.bookTheme}>
            {placeholderStory.theme}
          </ThemedText>
          <View style={styles.bookIcon}>
            <Book size={48} color="white" />
          </View>
        </ThemedView>

        {/* Story Content */}
        <ThemedView backgroundColor="card" style={styles.storyContent}>
          {/* Opening */}
          <View style={styles.section}>
            <View style={styles.dropCap}>
              <ThemedText style={styles.dropCapLetter}>
                {placeholderStory.opening.charAt(0)}
              </ThemedText>
            </View>
            <ThemedText style={styles.openingText}>
              {placeholderStory.opening.substring(1)}
            </ThemedText>
          </View>

          {/* Chapters */}
          {placeholderStory.chapters.map((chapter, index) => (
            <View key={index} style={styles.section}>
              <ThemedText variant="subtitle" style={styles.chapterTitle}>
                {chapter.title}
              </ThemedText>
              <ThemedText style={styles.chapterText}>
                {chapter.text}
              </ThemedText>
            </View>
          ))}

          {/* Conclusion */}
          <View style={styles.section}>
            <ThemedText variant="subtitle" style={styles.chapterTitle}>
              The Revelation
            </ThemedText>
            <ThemedText style={styles.chapterText}>
              {placeholderStory.conclusion}
            </ThemedText>
          </View>
        </ThemedView>

        {/* Action Buttons */}
        <ThemedView backgroundColor="card" style={styles.actionsCard}>
          <ThemedText variant="subtitle" style={styles.actionsTitle}>
            Story Actions
          </ThemedText>
          
          <View style={styles.actionButtons}>
            <ThemedView style={styles.actionButton}>
              <Play size={20} color={colors.textMuted} />
              <ThemedText color="textMuted" style={styles.actionText}>
                Text-to-Speech (Task 9)
              </ThemedText>
            </ThemedView>
            
            <ThemedView style={styles.actionButton}>
              <Share size={20} color={colors.textMuted} />
              <ThemedText color="textMuted" style={styles.actionText}>
                Share Story (Task 10)
              </ThemedText>
            </ThemedView>
            
            <ThemedView style={styles.actionButton}>
              <Download size={20} color={colors.textMuted} />
              <ThemedText color="textMuted" style={styles.actionText}>
                Download (Task 10)
              </ThemedText>
            </ThemedView>
          </View>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 32,
  },
  bookCover: {
    padding: 48,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  bookTitle: {
    fontSize: 28,
    fontFamily: 'serif',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 36,
  },
  bookTheme: {
    fontSize: 18,
    fontFamily: 'serif',
    color: 'white',
    textAlign: 'center',
    fontStyle: 'italic',
    opacity: 0.9,
    marginBottom: 32,
  },
  bookIcon: {
    opacity: 0.8,
  },
  storyContent: {
    margin: 24,
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  section: {
    marginBottom: 32,
  },
  dropCap: {
    position: 'absolute',
    left: 0,
    top: 0,
    zIndex: 1,
  },
  dropCapLetter: {
    fontSize: 64,
    fontFamily: 'serif',
    lineHeight: 64,
    color: '#a16207',
  },
  openingText: {
    fontSize: 16,
    fontFamily: 'serif',
    lineHeight: 24,
    paddingLeft: 48,
    paddingTop: 8,
  },
  chapterTitle: {
    fontSize: 20,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 24,
  },
  chapterText: {
    fontSize: 16,
    fontFamily: 'serif',
    lineHeight: 24,
  },
  actionsCard: {
    margin: 24,
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionsTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 16,
  },
  actionButtons: {
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'serif',
  },
});
