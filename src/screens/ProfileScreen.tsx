import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Camera, Trash2 } from 'lucide-react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuthHelpers } from '../hooks/useAuthHelpers';
import { ThemedView, ThemedText } from '../components/common';

export default function ProfileScreen() {
  const { colors } = useTheme();
  const { user, getDisplayName } = useAuthHelpers();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Picture */}
        <ThemedView backgroundColor="card" style={styles.avatarCard}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <User size={48} color={colors.textSecondary} />
            </View>
            <ThemedView style={styles.cameraButton}>
              <Camera size={16} color={colors.textMuted} />
            </ThemedView>
          </View>
          <ThemedText color="textMuted" style={styles.avatarNote}>
            Avatar upload will be implemented in Task 12
          </ThemedText>
        </ThemedView>

        {/* Profile Information */}
        <ThemedView backgroundColor="card" style={styles.infoCard}>
          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Profile Information
          </ThemedText>
          
          <View style={styles.infoRow}>
            <ThemedText style={styles.infoLabel}>Display Name</ThemedText>
            <ThemedText color="textSecondary" style={styles.infoValue}>
              {getDisplayName()}
            </ThemedText>
          </View>
          
          <View style={styles.infoRow}>
            <ThemedText style={styles.infoLabel}>Email</ThemedText>
            <ThemedText color="textSecondary" style={styles.infoValue}>
              {user?.email}
            </ThemedText>
          </View>
          
          <View style={styles.infoRow}>
            <ThemedText style={styles.infoLabel}>Member Since</ThemedText>
            <ThemedText color="textSecondary" style={styles.infoValue}>
              {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
            </ThemedText>
          </View>

          <ThemedView style={styles.editPlaceholder}>
            <ThemedText color="textMuted" style={styles.placeholderText}>
              Profile editing will be implemented in Task 12
            </ThemedText>
          </ThemedView>
        </ThemedView>

        {/* Account Actions */}
        <ThemedView backgroundColor="card" style={styles.actionsCard}>
          <ThemedText variant="subtitle" style={styles.sectionTitle}>
            Account Actions
          </ThemedText>
          
          <ThemedView style={styles.dangerZone}>
            <View style={styles.dangerHeader}>
              <Trash2 size={20} color={colors.error} />
              <ThemedText style={[styles.dangerTitle, { color: colors.error }]}>
                Delete Account
              </ThemedText>
            </View>
            <ThemedText color="textSecondary" style={styles.dangerDescription}>
              Permanently delete your account and all associated data. This action cannot be undone.
            </ThemedText>
            <ThemedView style={styles.deletePlaceholder}>
              <ThemedText color="textMuted" style={styles.placeholderText}>
                Account deletion will be implemented in Task 12
              </ThemedText>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  avatarCard: {
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 96,
    height: 96,
    borderRadius: 48,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#e5e7eb',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  avatarNote: {
    fontSize: 12,
    fontFamily: 'serif',
    textAlign: 'center',
  },
  infoCard: {
    borderRadius: 12,
    padding: 24,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  infoLabel: {
    fontSize: 16,
    fontFamily: 'serif',
  },
  infoValue: {
    fontSize: 16,
    fontFamily: 'serif',
    flex: 1,
    textAlign: 'right',
  },
  editPlaceholder: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
  },
  placeholderText: {
    fontSize: 12,
    fontFamily: 'serif',
    textAlign: 'center',
  },
  actionsCard: {
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dangerZone: {
    borderWidth: 1,
    borderColor: '#fecaca',
    borderRadius: 8,
    padding: 16,
    backgroundColor: '#fef2f2',
  },
  dangerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  dangerTitle: {
    fontSize: 16,
    fontFamily: 'serif',
    fontWeight: 'bold',
  },
  dangerDescription: {
    fontSize: 14,
    fontFamily: 'serif',
    lineHeight: 20,
    marginBottom: 12,
  },
  deletePlaceholder: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
    backgroundColor: 'white',
  },
});
