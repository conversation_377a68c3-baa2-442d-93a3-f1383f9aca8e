import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { BookOpen, Search, Calendar } from 'lucide-react-native';
import { useTheme } from '../context/ThemeContext';
import { ThemedView, ThemedText } from '../components/common';

export default function HistoryListScreen() {
  const { colors } = useTheme();

  // Placeholder data - will be replaced with real data from Supabase in Task 8
  const placeholderStories = [
    {
      id: '1',
      title: 'The Origin of Coffee',
      topic: 'Coffee',
      createdAt: '2024-01-15',
      preview: 'In the ancient highlands of Ethiopia, where the morning mist dances...',
    },
    {
      id: '2', 
      title: 'The Birth of the Internet',
      topic: 'The Internet',
      createdAt: '2024-01-14',
      preview: 'In the corridors of ARPANET, visionaries dreamed of connection...',
    },
    {
      id: '3',
      title: 'Democracy\'s Dawn',
      topic: 'Democracy',
      createdAt: '2024-01-13',
      preview: 'In the marble halls of ancient Athens, citizens gathered...',
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Search Section */}
      <ThemedView style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color={colors.textMuted} />
          <ThemedText color="textMuted" style={styles.searchPlaceholder}>
            Search your stories... (Coming in Task 8)
          </ThemedText>
        </View>
      </ThemedView>

      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {placeholderStories.length > 0 ? (
          <>
            <ThemedText variant="subtitle" style={styles.sectionTitle}>
              Your Stories
            </ThemedText>
            
            {placeholderStories.map((story) => (
              <ThemedView key={story.id} backgroundColor="card" style={styles.storyCard}>
                <View style={styles.storyHeader}>
                  <View style={styles.storyTitleContainer}>
                    <BookOpen size={20} color={colors.accent} />
                    <ThemedText variant="subtitle" style={styles.storyTitle}>
                      {story.title}
                    </ThemedText>
                  </View>
                  <View style={styles.storyDate}>
                    <Calendar size={16} color={colors.textMuted} />
                    <ThemedText color="textMuted" style={styles.dateText}>
                      {new Date(story.createdAt).toLocaleDateString()}
                    </ThemedText>
                  </View>
                </View>
                
                <ThemedText color="textSecondary" style={styles.storyTopic}>
                  Topic: {story.topic}
                </ThemedText>
                
                <ThemedText color="textSecondary" style={styles.storyPreview}>
                  {story.preview}
                </ThemedText>
                
                <ThemedView style={styles.placeholderNote}>
                  <ThemedText color="textMuted" style={styles.placeholderText}>
                    Story interaction will be implemented in Task 8
                  </ThemedText>
                </ThemedView>
              </ThemedView>
            ))}
          </>
        ) : (
          <ThemedView style={styles.emptyState}>
            <BookOpen size={64} color={colors.textMuted} />
            <ThemedText variant="subtitle" color="textMuted" style={styles.emptyTitle}>
              No Stories Yet
            </ThemedText>
            <ThemedText color="textMuted" style={styles.emptyDescription}>
              Create your first origin story to see it here
            </ThemedText>
          </ThemedView>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  searchPlaceholder: {
    fontSize: 16,
    fontFamily: 'serif',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'serif',
    marginBottom: 16,
  },
  storyCard: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  storyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  storyTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  storyTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    flex: 1,
  },
  storyDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dateText: {
    fontSize: 12,
    fontFamily: 'serif',
  },
  storyTopic: {
    fontSize: 14,
    fontFamily: 'serif',
    marginBottom: 8,
  },
  storyPreview: {
    fontSize: 14,
    fontFamily: 'serif',
    lineHeight: 20,
    marginBottom: 12,
  },
  placeholderNote: {
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
  },
  placeholderText: {
    fontSize: 12,
    fontFamily: 'serif',
    textAlign: 'center',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'serif',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    fontFamily: 'serif',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});
