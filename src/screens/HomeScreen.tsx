import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Book, Feather } from 'lucide-react-native';
import { useTheme } from '../context/ThemeContext';
import { ThemedView, ThemedText, ThemeToggle } from '../components/common';

export default function HomeScreen() {
  const { colors } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header with theme toggle */}
      <ThemedView style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Book size={24} color={colors.accent} />
            <ThemedText variant="title" style={styles.title}>
              Origin Stories
            </ThemedText>
            <Feather size={24} color={colors.accent} />
          </View>
          <ThemedText variant="body" color="textSecondary" style={styles.subtitle}>
            Discover the legendary beginnings of anything your heart desires
          </ThemedText>
        </View>
        <View style={styles.themeToggleContainer}>
          <ThemeToggle />
        </View>
      </ThemedView>

      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Story Input Section */}
        <ThemedView backgroundColor="card" style={styles.inputCard}>
          <ThemedText variant="subtitle" style={styles.inputTitle}>
            What origin story shall we tell?
          </ThemedText>
          <ThemedText color="textSecondary" style={styles.inputDescription}>
            Enter anything - an object, concept, tradition, or invention
          </ThemedText>
          
          {/* Placeholder for story input interface - will be implemented in Task 6 */}
          <ThemedView style={styles.placeholderContainer}>
            <ThemedText color="textMuted" style={styles.placeholderText}>
              Story generation interface will be implemented in Task 6
            </ThemedText>
            <ThemedText color="textMuted" style={styles.placeholderSubtext}>
              This will include the elegant input design, animated progress indicators, 
              and story display from the original artifact
            </ThemedText>
          </ThemedView>
        </ThemedView>

        {/* Quick Start Examples */}
        <ThemedView backgroundColor="card" style={styles.examplesCard}>
          <ThemedText variant="subtitle" style={styles.examplesTitle}>
            Popular Origins
          </ThemedText>
          <View style={styles.examplesGrid}>
            {['Coffee', 'The Internet', 'Democracy', 'Pizza', 'Music', 'Writing'].map((example, index) => (
              <ThemedView key={index} style={styles.exampleChip}>
                <ThemedText color="textSecondary" style={styles.exampleText}>
                  {example}
                </ThemedText>
              </ThemedView>
            ))}
          </View>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  title: {
    fontSize: 32,
    fontFamily: 'serif',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'serif',
    fontStyle: 'italic',
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  themeToggleContainer: {
    marginTop: 8,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  inputCard: {
    borderRadius: 12,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputTitle: {
    fontSize: 20,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 8,
  },
  inputDescription: {
    fontSize: 14,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 24,
  },
  placeholderContainer: {
    padding: 32,
    borderRadius: 8,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    fontFamily: 'serif',
    textAlign: 'center',
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 12,
    fontFamily: 'serif',
    textAlign: 'center',
    lineHeight: 18,
  },
  examplesCard: {
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  examplesTitle: {
    fontSize: 18,
    fontFamily: 'serif',
    marginBottom: 16,
    textAlign: 'center',
  },
  examplesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    justifyContent: 'center',
  },
  exampleChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  exampleText: {
    fontSize: 14,
    fontFamily: 'serif',
  },
});
