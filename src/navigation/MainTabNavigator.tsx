import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Book, History, Settings, User } from 'lucide-react-native';
import { useTheme } from '../context/ThemeContext';
import { MainTabParamList, HistoryStackParamList, SettingsStackParamList } from '../types/navigation';

// Import screens (will be created)
import HomeScreen from '../screens/HomeScreen';
import HistoryListScreen from '../screens/HistoryListScreen';
import StoryViewerScreen from '../screens/StoryViewerScreen';
import SettingsMainScreen from '../screens/SettingsMainScreen';
import SubscriptionScreen from '../screens/SubscriptionScreen';
import ProfileScreen from '../screens/ProfileScreen';
import TermsScreen from '../screens/TermsScreen';
import PrivacyScreen from '../screens/PrivacyScreen';

const Tab = createBottomTabNavigator<MainTabParamList>();
const HistoryStack = createStackNavigator<HistoryStackParamList>();
const SettingsStack = createStackNavigator<SettingsStackParamList>();

// History Stack Navigator
function HistoryStackNavigator() {
  const { colors } = useTheme();
  
  return (
    <HistoryStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontFamily: 'serif',
          fontSize: 18,
        },
        cardStyle: { backgroundColor: colors.background },
      }}
    >
      <HistoryStack.Screen 
        name="HistoryList" 
        component={HistoryListScreen}
        options={{
          title: 'Story History',
          headerShown: true,
        }}
      />
      <HistoryStack.Screen 
        name="StoryViewer" 
        component={StoryViewerScreen}
        options={{
          title: 'Story',
          headerShown: true,
          headerBackTitleVisible: false,
        }}
      />
    </HistoryStack.Navigator>
  );
}

// Settings Stack Navigator
function SettingsStackNavigator() {
  const { colors } = useTheme();
  
  return (
    <SettingsStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontFamily: 'serif',
          fontSize: 18,
        },
        cardStyle: { backgroundColor: colors.background },
      }}
    >
      <SettingsStack.Screen 
        name="SettingsMain" 
        component={SettingsMainScreen}
        options={{
          title: 'Settings',
          headerShown: true,
        }}
      />
      <SettingsStack.Screen 
        name="Subscription" 
        component={SubscriptionScreen}
        options={{
          title: 'Subscription',
          headerShown: true,
          headerBackTitleVisible: false,
        }}
      />
      <SettingsStack.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          title: 'Profile',
          headerShown: true,
          headerBackTitleVisible: false,
        }}
      />
      <SettingsStack.Screen 
        name="Terms" 
        component={TermsScreen}
        options={{
          title: 'Terms of Use',
          headerShown: true,
          headerBackTitleVisible: false,
        }}
      />
      <SettingsStack.Screen 
        name="Privacy" 
        component={PrivacyScreen}
        options={{
          title: 'Privacy Policy',
          headerShown: true,
          headerBackTitleVisible: false,
        }}
      />
    </SettingsStack.Navigator>
  );
}

// Main Tab Navigator
export default function MainTabNavigator() {
  const { colors, isDark } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let IconComponent;

          if (route.name === 'Home') {
            IconComponent = Book;
          } else if (route.name === 'History') {
            IconComponent = History;
          } else if (route.name === 'Settings') {
            IconComponent = Settings;
          }

          return IconComponent ? (
            <IconComponent 
              size={size} 
              color={color}
              strokeWidth={focused ? 2.5 : 2}
            />
          ) : null;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textMuted,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: 8,
          height: 60,
        },
        tabBarLabelStyle: {
          fontFamily: 'serif',
          fontSize: 12,
          marginTop: 4,
        },
        headerShown: false,
        tabBarHideOnKeyboard: true,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{
          tabBarLabel: 'Create',
        }}
      />
      <Tab.Screen 
        name="History" 
        component={HistoryStackNavigator}
        options={{
          tabBarLabel: 'History',
        }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsStackNavigator}
        options={{
          tabBarLabel: 'Settings',
        }}
      />
    </Tab.Navigator>
  );
}
