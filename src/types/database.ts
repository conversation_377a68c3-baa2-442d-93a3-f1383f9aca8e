export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          avatar_storage_path: string | null
          subscription_tier: 'free' | 'plus' | 'premium' | 'pro'
          subscription_expires_at: string | null
          story_count_current_month: number
          settings: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          avatar_storage_path?: string | null
          subscription_tier?: 'free' | 'plus' | 'premium' | 'pro'
          subscription_expires_at?: string | null
          story_count_current_month?: number
          settings?: J<PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          avatar_storage_path?: string | null
          subscription_tier?: 'free' | 'plus' | 'premium' | 'pro'
          subscription_expires_at?: string | null
          story_count_current_month?: number
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
      }
      stories: {
        Row: {
          id: string
          user_id: string
          topic: string
          title: string
          theme: string | null
          opening: string
          chapters: Json
          conclusion: string
          settings: Json
          audio_file_path: string | null
          pdf_file_path: string | null
          docx_file_path: string | null
          illustration_paths: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          topic: string
          title: string
          theme?: string | null
          opening: string
          chapters?: Json
          conclusion: string
          settings?: Json
          audio_file_path?: string | null
          pdf_file_path?: string | null
          docx_file_path?: string | null
          illustration_paths?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          topic?: string
          title?: string
          theme?: string | null
          opening?: string
          chapters?: Json
          conclusion?: string
          settings?: Json
          audio_file_path?: string | null
          pdf_file_path?: string | null
          docx_file_path?: string | null
          illustration_paths?: Json
          created_at?: string
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_subscription_id: string | null
          stripe_customer_id: string | null
          status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid'
          tier: 'free' | 'plus' | 'premium' | 'pro'
          current_period_start: string | null
          current_period_end: string | null
          cancel_at_period_end: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_subscription_id?: string | null
          stripe_customer_id?: string | null
          status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid'
          tier: 'free' | 'plus' | 'premium' | 'pro'
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_subscription_id?: string | null
          stripe_customer_id?: string | null
          status?: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid'
          tier?: 'free' | 'plus' | 'premium' | 'pro'
          current_period_start?: string | null
          current_period_end?: string | null
          cancel_at_period_end?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      user_stories_view: {
        Row: {
          id: string
          user_id: string
          topic: string
          title: string
          theme: string | null
          opening: string
          chapters: Json
          conclusion: string
          settings: Json
          created_at: string
          updated_at: string
          estimated_word_count: number
        }
      }
    }
    Functions: {
      can_generate_story: {
        Args: {
          user_uuid: string
        }
        Returns: boolean
      }
      delete_user_account: {
        Args: {
          user_uuid: string
        }
        Returns: undefined
      }
      get_remaining_stories: {
        Args: {
          user_uuid: string
        }
        Returns: number
      }
      get_user_story_limit: {
        Args: {
          user_uuid: string
        }
        Returns: number
      }
      get_user_subscription_info: {
        Args: {
          user_uuid: string
        }
        Returns: {
          tier: string
          expires_at: string | null
          stories_used: number
          stories_limit: number
          stories_remaining: number
          is_active: boolean
        }[]
      }
      is_subscription_active: {
        Args: {
          user_uuid: string
        }
        Returns: boolean
      }
      reset_monthly_story_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      search_user_stories: {
        Args: {
          user_uuid: string
          search_term: string
        }
        Returns: {
          id: string
          topic: string
          title: string
          theme: string | null
          created_at: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Helper types for the application
export type User = Database['public']['Tables']['users']['Row']
export type Story = Database['public']['Tables']['stories']['Row']
export type Subscription = Database['public']['Tables']['subscriptions']['Row']

export type StoryInsert = Database['public']['Tables']['stories']['Insert']
export type UserUpdate = Database['public']['Tables']['users']['Update']

export interface StoryChapter {
  title: string
  text: string
}

export interface StorySettings {
  ageGroup: 'children' | 'kids' | 'teens' | 'adults' | 'professional'
  tone: 'funny' | 'inspiring' | 'epic' | 'professional' | 'standard'
}

export interface UserSettings {
  defaultAgeGroup?: string
  defaultTone?: string
  voiceEnabled?: boolean
  voiceStyle?: string
  darkMode?: boolean
}