// Core type definitions for the Origin Stories App

export interface User {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  subscriptionTier: 'free' | 'plus' | 'premium' | 'pro';
  subscriptionExpiresAt?: string;
  storyCountCurrentMonth: number;
  settings: UserSettings;
}

export interface UserSettings {
  defaultAgeGroup: string;
  defaultTone: string;
  voiceEnabled: boolean;
  voiceStyle?: string;
  darkMode: boolean;
}

export interface Story {
  id: string;
  userId: string;
  topic: string;
  title: string;
  theme: string;
  opening: string;
  chapters: Chapter[];
  conclusion: string;
  settings: StorySettings;
  createdAt: string;
}

export interface Chapter {
  title: string;
  text: string;
}

export interface StorySettings {
  ageGroup: 'children' | 'kids' | 'teens' | 'adults' | 'professional';
  tone: 'funny' | 'inspiring' | 'epic' | 'professional' | 'standard';
  voiceEnabled: boolean;
  voiceStyle?: string;
}

export interface AppError {
  type: 'network' | 'auth' | 'validation' | 'subscription' | 'generation';
  message: string;
  code?: string;
  retryable: boolean;
}

// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  History: undefined;
  Settings: undefined;
  Subscription: undefined;
  StoryViewer: { storyId: string };
};

// Theme types
export interface ColorScheme {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  success: string;
}

export interface FontScheme {
  regular: string;
  medium: string;
  bold: string;
  serif: string;
}