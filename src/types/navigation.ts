// Navigation type definitions for the Origin Stories App

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  History: undefined;
  Settings: undefined;
};

export type HistoryStackParamList = {
  HistoryList: undefined;
  StoryViewer: { storyId: string };
};

export type SettingsStackParamList = {
  SettingsMain: undefined;
  Subscription: undefined;
  Profile: undefined;
  Terms: undefined;
  Privacy: undefined;
};

// Navigation guard types
export type NavigationGuardType = 'auth' | 'subscription';

export interface NavigationGuardConfig {
  type: NavigationGuardType;
  requiredTier?: 'free' | 'plus' | 'premium' | 'pro';
  fallbackRoute?: string;
}