import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import * as SecureStore from 'expo-secure-store';

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeColors {
  background: string;
  surface: string;
  card: string;
  text: string;
  textSecondary: string;
  textMuted: string;
  primary: string;
  primaryLight: string;
  primaryDark: string;
  accent: string;
  accentLight: string;
  border: string;
  divider: string;
}

interface ThemeContextType {
  mode: ThemeMode;
  isDark: boolean;
  colors: ThemeColors;
  setTheme: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = 'app_theme_mode';

// Light theme colors (amber/brown scheme)
const lightColors: ThemeColors = {
  background: '#fffbeb', // amber-50
  surface: '#ffffff',
  card: '#fef3c7', // amber-100
  text: '#78350f', // amber-900
  textSecondary: '#92400e', // amber-800
  textMuted: '#b45309', // amber-700
  primary: '#d97706', // amber-600
  primaryLight: '#fbbf24', // amber-400
  primaryDark: '#92400e', // amber-800
  accent: '#f59e0b', // amber-500
  accentLight: '#fcd34d', // amber-300
  border: '#fde68a', // amber-200
  divider: '#fed7aa', // amber-200 with slight variation
};

// Dark theme colors (maintaining amber warmth but darker)
const darkColors: ThemeColors = {
  background: '#0f0f0f', // Very dark background
  surface: '#1a1a1a', // Dark surface
  card: '#2d1b0f', // Dark amber-tinted card
  text: '#fef3c7', // Light amber for text
  textSecondary: '#fde68a', // Amber-200 for secondary text
  textMuted: '#fbbf24', // Amber-400 for muted text
  primary: '#f59e0b', // Amber-500 for primary
  primaryLight: '#fcd34d', // Amber-300 for light primary
  primaryDark: '#d97706', // Amber-600 for dark primary
  accent: '#fbbf24', // Amber-400 for accent
  accentLight: '#fde68a', // Amber-200 for light accent
  border: '#451a03', // Dark amber border
  divider: '#78350f', // Amber-900 for dividers
};

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [mode, setMode] = useState<ThemeMode>('system');
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );

  // Initialize theme from storage
  useEffect(() => {
    loadThemeFromStorage();
    
    // Listen for system theme changes
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      setSystemColorScheme(colorScheme);
    });

    return () => subscription.remove();
  }, []);

  const loadThemeFromStorage = async () => {
    try {
      const storedTheme = await SecureStore.getItemAsync(THEME_STORAGE_KEY);
      if (storedTheme && ['light', 'dark', 'system'].includes(storedTheme)) {
        setMode(storedTheme as ThemeMode);
      }
    } catch (error) {
      console.error('Error loading theme from storage:', error);
    }
  };

  const saveThemeToStorage = async (themeMode: ThemeMode) => {
    try {
      await SecureStore.setItemAsync(THEME_STORAGE_KEY, themeMode);
    } catch (error) {
      console.error('Error saving theme to storage:', error);
    }
  };

  const setTheme = (newMode: ThemeMode) => {
    setMode(newMode);
    saveThemeToStorage(newMode);
  };

  const toggleTheme = () => {
    const newMode = mode === 'light' ? 'dark' : 'light';
    setTheme(newMode);
  };

  // Determine if dark mode should be active
  const isDark = mode === 'dark' || (mode === 'system' && systemColorScheme === 'dark');

  // Get current theme colors
  const colors = isDark ? darkColors : lightColors;

  const value: ThemeContextType = {
    mode,
    isDark,
    colors,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook for getting theme-aware styles
export function useThemedStyles<T>(
  styleCreator: (colors: ThemeColors, isDark: boolean) => T
): T {
  const { colors, isDark } = useTheme();
  return styleCreator(colors, isDark);
}

// Utility function to create theme-aware StyleSheet
export function createThemedStyles<T extends Record<string, any>>(
  styleCreator: (colors: ThemeColors, isDark: boolean) => T
) {
  return (colors: ThemeColors, isDark: boolean) => styleCreator(colors, isDark);
}