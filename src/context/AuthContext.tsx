import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Session, User as SupabaseUser, AuthError } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';
import { supabase, supabaseHelpers } from '../services/supabase';
import { User } from '../types/database';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName?: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (password: string) => Promise<void>;
  refreshSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Secure storage keys
const SESSION_KEY = 'supabase_session';
const USER_KEY = 'user_profile';

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
    
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        
        if (session) {
          await handleSessionUpdate(session);
        } else {
          await handleSignOut();
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const initializeAuth = async () => {
    try {
      // Try to get session from Supabase first
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Error getting session:', error);
        await clearStoredAuth();
        setLoading(false);
        return;
      }

      if (session) {
        await handleSessionUpdate(session);
      } else {
        // Try to restore from secure storage
        await restoreFromStorage();
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      await clearStoredAuth();
    } finally {
      setLoading(false);
    }
  };

  const handleSessionUpdate = async (newSession: Session) => {
    try {
      setSession(newSession);
      
      // Store session securely
      await SecureStore.setItemAsync(SESSION_KEY, JSON.stringify(newSession));
      
      // Get or create user profile
      const userProfile = await getOrCreateUserProfile(newSession.user);
      setUser(userProfile);
      
      // Store user profile
      await SecureStore.setItemAsync(USER_KEY, JSON.stringify(userProfile));
    } catch (error) {
      console.error('Error handling session update:', error);
      throw new Error('Failed to update user session');
    }
  };

  const handleSignOut = async () => {
    setSession(null);
    setUser(null);
    await clearStoredAuth();
  };

  const restoreFromStorage = async () => {
    try {
      const storedSession = await SecureStore.getItemAsync(SESSION_KEY);
      const storedUser = await SecureStore.getItemAsync(USER_KEY);
      
      if (storedSession && storedUser) {
        const parsedSession = JSON.parse(storedSession);
        const parsedUser = JSON.parse(storedUser);
        
        // Verify session is still valid
        const { data: { user: currentUser }, error } = await supabase.auth.getUser();
        
        if (!error && currentUser && currentUser.id === parsedUser.id) {
          setSession(parsedSession);
          setUser(parsedUser);
        } else {
          await clearStoredAuth();
        }
      }
    } catch (error) {
      console.error('Error restoring from storage:', error);
      await clearStoredAuth();
    }
  };

  const clearStoredAuth = async () => {
    try {
      await SecureStore.deleteItemAsync(SESSION_KEY);
      await SecureStore.deleteItemAsync(USER_KEY);
    } catch (error) {
      console.error('Error clearing stored auth:', error);
    }
  };

  const getOrCreateUserProfile = async (supabaseUser: SupabaseUser): Promise<User> => {
    try {
      // Try to get existing user profile
      const existingProfile = await supabaseHelpers.getUserProfile(supabaseUser.id);
      return existingProfile;
    } catch (error) {
      // If user doesn't exist, create new profile
      const newUserData = {
        id: supabaseUser.id,
        email: supabaseUser.email!,
        full_name: supabaseUser.user_metadata?.full_name || null,
        avatar_url: supabaseUser.user_metadata?.avatar_url || null,
        subscription_tier: 'free' as const,
        story_count_current_month: 0,
        settings: {
          defaultAgeGroup: 'adults',
          defaultTone: 'standard',
          voiceEnabled: false,
          darkMode: false,
        },
      };

      const { data, error: insertError } = await supabase
        .from('users')
        .insert(newUserData)
        .select()
        .single();

      if (insertError) {
        throw insertError;
      }

      return data;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim().toLowerCase(),
        password,
      });

      if (error) {
        throw error;
      }

      // Session will be handled by onAuthStateChange
    } catch (error) {
      console.error('Sign in error:', error);
      throw new Error(getAuthErrorMessage(error as AuthError));
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signUp({
        email: email.trim().toLowerCase(),
        password,
        options: {
          data: {
            full_name: fullName?.trim() || null,
          },
        },
      });

      if (error) {
        throw error;
      }

      // If email confirmation is required, user will need to check their email
      if (!data.session) {
        throw new Error('Please check your email to confirm your account before signing in.');
      }

      // Session will be handled by onAuthStateChange
    } catch (error) {
      console.error('Sign up error:', error);
      throw new Error(getAuthErrorMessage(error as AuthError));
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'exp://localhost:8081/--/auth/callback', // Adjust for your app
        },
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Google sign in error:', error);
      throw new Error('Failed to sign in with Google. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const signInWithApple = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: 'exp://localhost:8081/--/auth/callback', // Adjust for your app
        },
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Apple sign in error:', error);
      throw new Error('Failed to sign in with Apple. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw error;
      }

      // Clear state will be handled by onAuthStateChange
    } catch (error) {
      console.error('Sign out error:', error);
      throw new Error('Failed to sign out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(
        email.trim().toLowerCase(),
        {
          redirectTo: 'exp://localhost:8081/--/auth/reset-password', // Adjust for your app
        }
      );

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Password reset error:', error);
      throw new Error('Failed to send password reset email. Please try again.');
    }
  };

  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password,
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Password update error:', error);
      throw new Error('Failed to update password. Please try again.');
    }
  };

  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        throw error;
      }

      if (data.session) {
        await handleSessionUpdate(data.session);
      }
    } catch (error) {
      console.error('Session refresh error:', error);
      throw new Error('Failed to refresh session. Please sign in again.');
    }
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signInWithApple,
    signOut,
    resetPassword,
    updatePassword,
    refreshSession,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper function to get user-friendly error messages
function getAuthErrorMessage(error: AuthError): string {
  switch (error.message) {
    case 'Invalid login credentials':
      return 'Invalid email or password. Please check your credentials and try again.';
    case 'Email not confirmed':
      return 'Please check your email and click the confirmation link before signing in.';
    case 'User already registered':
      return 'An account with this email already exists. Please sign in instead.';
    case 'Password should be at least 6 characters':
      return 'Password must be at least 6 characters long.';
    case 'Unable to validate email address: invalid format':
      return 'Please enter a valid email address.';
    case 'Signup is disabled':
      return 'Account registration is currently disabled. Please contact support.';
    default:
      return error.message || 'An unexpected error occurred. Please try again.';
  }
}