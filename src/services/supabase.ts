import { createClient } from '@supabase/supabase-js'
import { Database } from '../types/database'

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.')
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
})

// Helper functions for common operations
export const supabaseHelpers = {
  // Get current user
  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) throw error
    return user
  },

  // Get user profile
  getUserProfile: async (userId: string) => {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) throw error
    return data
  },

  // Get user subscription info
  getUserSubscriptionInfo: async (userId: string) => {
    const { data, error } = await supabase
      .rpc('get_user_subscription_info', { user_uuid: userId })
    
    if (error) throw error
    return data[0]
  },

  // Check if user can generate story
  canGenerateStory: async (userId: string) => {
    const { data, error } = await supabase
      .rpc('can_generate_story', { user_uuid: userId })
    
    if (error) throw error
    return data
  },

  // Get user stories with pagination
  getUserStories: async (userId: string, page = 0, limit = 20) => {
    const { data, error } = await supabase
      .from('stories')
      .select('id, topic, title, theme, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)
    
    if (error) throw error
    return data
  },

  // Search user stories
  searchUserStories: async (userId: string, searchTerm: string) => {
    const { data, error } = await supabase
      .rpc('search_user_stories', { 
        user_uuid: userId, 
        search_term: searchTerm 
      })
    
    if (error) throw error
    return data
  },

  // Get full story by ID
  getStoryById: async (storyId: string) => {
    const { data, error } = await supabase
      .from('stories')
      .select('*')
      .eq('id', storyId)
      .single()
    
    if (error) throw error
    return data
  },

  // Delete story
  deleteStory: async (storyId: string) => {
    const { error } = await supabase
      .from('stories')
      .delete()
      .eq('id', storyId)
    
    if (error) throw error
  },

  // Update user profile
  updateUserProfile: async (userId: string, updates: any) => {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete user account
  deleteUserAccount: async (userId: string) => {
    const { error } = await supabase
      .rpc('delete_user_account', { user_uuid: userId })
    
    if (error) throw error
  },

  // Storage helpers
  // Upload user avatar
  uploadAvatar: async (userId: string, file: File) => {
    const fileExt = file.name.split('.').pop()
    const fileName = `${userId}/avatar.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from('user-avatars')
      .upload(fileName, file, { upsert: true })
    
    if (error) throw error
    
    // Update user profile with storage path
    await supabaseHelpers.updateUserProfile(userId, {
      avatar_storage_path: fileName
    })
    
    return data
  },

  // Get avatar URL
  getAvatarUrl: async (avatarPath: string) => {
    const { data } = await supabase.storage
      .from('user-avatars')
      .getPublicUrl(avatarPath)
    
    return data.publicUrl
  },

  // Generate story audio
  generateStoryAudio: async (storyId: string, options?: { voiceStyle?: string, speed?: number }) => {
    const { data, error } = await supabase.functions.invoke('generate-story-audio', {
      body: {
        storyId,
        voiceStyle: options?.voiceStyle || 'standard',
        speed: options?.speed || 1.0
      }
    })
    
    if (error) throw error
    return data
  },

  // Export story document
  exportStoryDocument: async (storyId: string, format: 'pdf' | 'docx' | 'txt', includeAudio = false) => {
    const { data, error } = await supabase.functions.invoke('export-story-document', {
      body: {
        storyId,
        format,
        includeAudio
      }
    })
    
    if (error) throw error
    return data
  },

  // Get signed URL for story files
  getStoryFileUrl: async (bucket: string, filePath: string) => {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, 3600)
    
    if (error) throw error
    return data.signedUrl
  },

  // Delete story file
  deleteStoryFile: async (bucket: string, filePath: string) => {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([filePath])
    
    if (error) throw error
  },
}