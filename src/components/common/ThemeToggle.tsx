import React from 'react';
import { TouchableOpacity, ViewStyle } from 'react-native';
import { ThemedText } from './ThemedText';
import { useTheme } from '../../context/ThemeContext';

interface ThemeToggleProps {
  style?: ViewStyle;
  showLabel?: boolean;
}

/**
 * A toggle button for switching between light and dark themes
 */
export function ThemeToggle({ style, showLabel = true }: ThemeToggleProps) {
  const { mode, isDark, setTheme, toggleTheme } = useTheme();

  const getThemeIcon = () => {
    if (mode === 'system') {
      return '🌓'; // Half moon for system
    }
    return isDark ? '🌙' : '☀️';
  };

  const getThemeLabel = () => {
    switch (mode) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
      default:
        return 'Theme';
    }
  };

  const cycleTheme = () => {
    switch (mode) {
      case 'light':
        setTheme('dark');
        break;
      case 'dark':
        setTheme('system');
        break;
      case 'system':
        setTheme('light');
        break;
    }
  };

  const buttonStyle: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: 'transparent',
  };

  return (
    <TouchableOpacity
      style={[buttonStyle, style]}
      onPress={cycleTheme}
      accessibilityLabel={`Switch theme. Current: ${getThemeLabel()}`}
      accessibilityRole="button"
    >
      <ThemedText variant="body" style={{ fontSize: 20, marginRight: showLabel ? 8 : 0 }}>
        {getThemeIcon()}
      </ThemedText>
      {showLabel && (
        <ThemedText color="textSecondary" variant="body">
          {getThemeLabel()}
        </ThemedText>
      )}
    </TouchableOpacity>
  );
}