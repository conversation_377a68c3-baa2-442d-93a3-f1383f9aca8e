import React from 'react';
import { View, ViewProps, ViewStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface ThemedViewProps extends ViewProps {
  backgroundColor?: 'background' | 'surface' | 'card';
  children?: React.ReactNode;
}

/**
 * A themed View component that automatically applies theme colors
 */
export function ThemedView({ 
  backgroundColor = 'background', 
  style, 
  children, 
  ...props 
}: ThemedViewProps) {
  const { colors } = useTheme();

  const themedStyle: ViewStyle = {
    backgroundColor: colors[backgroundColor],
  };

  return (
    <View style={[themedStyle, style]} {...props}>
      {children}
    </View>
  );
}