import React from 'react';
import { Text, TextProps, TextStyle } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface ThemedTextProps extends TextProps {
  color?: 'text' | 'textSecondary' | 'textMuted' | 'primary' | 'accent';
  variant?: 'title' | 'subtitle' | 'body' | 'caption';
  children?: React.ReactNode;
}

/**
 * A themed Text component that automatically applies theme colors and typography
 */
export function ThemedText({ 
  color = 'text', 
  variant = 'body',
  style, 
  children, 
  ...props 
}: ThemedTextProps) {
  const { colors } = useTheme();

  const getVariantStyle = (): TextStyle => {
    switch (variant) {
      case 'title':
        return {
          fontSize: 24,
          fontFamily: 'serif',
          fontWeight: '600',
        };
      case 'subtitle':
        return {
          fontSize: 18,
          fontFamily: 'serif',
          fontWeight: '500',
        };
      case 'body':
        return {
          fontSize: 16,
          fontFamily: 'serif',
        };
      case 'caption':
        return {
          fontSize: 14,
          fontFamily: 'serif',
        };
      default:
        return {};
    }
  };

  const themedStyle: TextStyle = {
    color: colors[color],
    ...getVariantStyle(),
  };

  return (
    <Text style={[themedStyle, style]} {...props}>
      {children}
    </Text>
  );
}