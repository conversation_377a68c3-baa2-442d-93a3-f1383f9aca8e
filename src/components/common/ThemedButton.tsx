import React from 'react';
import { TouchableOpacity, TouchableOpacityProps, ViewStyle, TextStyle, ActivityIndicator } from 'react-native';
import { ThemedText } from './ThemedText';
import { useTheme } from '../../context/ThemeContext';

interface ThemedButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  children?: React.ReactNode;
}

/**
 * A themed Button component with consistent styling across the app
 */
export function ThemedButton({ 
  title,
  variant = 'primary',
  size = 'medium',
  loading = false,
  style, 
  disabled,
  children,
  ...props 
}: ThemedButtonProps) {
  const { colors } = useTheme();

  const getSizeStyle = (): ViewStyle => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: 8,
          paddingHorizontal: 16,
          borderRadius: 6,
        };
      case 'medium':
        return {
          paddingVertical: 12,
          paddingHorizontal: 24,
          borderRadius: 8,
        };
      case 'large':
        return {
          paddingVertical: 16,
          paddingHorizontal: 32,
          borderRadius: 10,
        };
      default:
        return {};
    }
  };

  const getVariantStyle = (): ViewStyle => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: colors.primary,
          borderWidth: 0,
        };
      case 'secondary':
        return {
          backgroundColor: colors.surface,
          borderWidth: 1,
          borderColor: colors.border,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.primary,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderWidth: 0,
        };
      default:
        return {};
    }
  };

  const getTextColor = (): 'text' | 'textSecondary' | 'textMuted' | 'primary' | 'accent' => {
    switch (variant) {
      case 'primary':
        return 'text'; // Will be white/light on primary background
      case 'secondary':
        return 'text';
      case 'outline':
        return 'primary';
      case 'ghost':
        return 'primary';
      default:
        return 'text';
    }
  };

  const getTextSize = (): 'body' | 'caption' | 'subtitle' => {
    switch (size) {
      case 'small':
        return 'caption';
      case 'medium':
        return 'body';
      case 'large':
        return 'subtitle';
      default:
        return 'body';
    }
  };

  const buttonStyle: ViewStyle = {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    ...getSizeStyle(),
    ...getVariantStyle(),
    opacity: (disabled || loading) ? 0.5 : 1,
  };

  // Override text color for primary variant to ensure readability
  const textStyle: TextStyle = variant === 'primary' ? { color: 'white' } : {};

  return (
    <TouchableOpacity
      style={[buttonStyle, style]}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <ActivityIndicator 
          color={variant === 'primary' ? 'white' : colors.primary} 
          size="small" 
        />
      ) : (
        <>
          <ThemedText 
            color={getTextColor()} 
            variant={getTextSize()}
            style={textStyle}
          >
            {title}
          </ThemedText>
          {children}
        </>
      )}
    </TouchableOpacity>
  );
}