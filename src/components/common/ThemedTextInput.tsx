import React, { useState } from 'react';
import { TextInput, TextInputProps, ViewStyle, TextStyle, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { useTheme } from '../../context/ThemeContext';

interface ThemedTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
}

/**
 * A themed TextInput component with consistent styling and optional label/error
 */
export function ThemedTextInput({ 
  label,
  error,
  containerStyle,
  style, 
  onFocus,
  onBlur,
  ...props 
}: ThemedTextInputProps) {
  const { colors } = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const inputStyle: TextStyle = {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: error ? '#ef4444' : (isFocused ? colors.primary : colors.border),
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: colors.text,
    fontFamily: 'serif',
    fontSize: 16,
  };

  const containerStyles: ViewStyle = {
    marginBottom: 16,
  };

  return (
    <View style={[containerStyles, containerStyle]}>
      {label && (
        <ThemedText 
          color="textSecondary" 
          variant="body"
          style={{ marginBottom: 8 }}
        >
          {label}
        </ThemedText>
      )}
      
      <TextInput
        style={[inputStyle, style]}
        placeholderTextColor={colors.textMuted}
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...props}
      />
      
      {error && (
        <ThemedText 
          color="textMuted"
          variant="caption"
          style={{ color: '#ef4444', marginTop: 4 }}
        >
          {error}
        </ThemedText>
      )}
    </View>
  );
}