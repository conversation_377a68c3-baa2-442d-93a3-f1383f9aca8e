import React from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { Book } from 'lucide-react-native';
import { useTheme } from '../../context/ThemeContext';
import { ThemedView, ThemedText } from './index';

export default function LoadingScreen() {
  const { colors } = useTheme();

  return (
    <ThemedView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Book size={48} color={colors.accent} />
        </View>
        
        <ThemedText variant="title" style={styles.title}>
          Origin Stories
        </ThemedText>
        
        <ThemedText color="textSecondary" style={styles.subtitle}>
          Loading your legendary beginnings...
        </ThemedText>
        
        <ActivityIndicator 
          size="large" 
          color={colors.primary} 
          style={styles.spinner}
        />
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontFamily: 'serif',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'serif',
    fontStyle: 'italic',
    textAlign: 'center',
    marginBottom: 32,
  },
  spinner: {
    marginTop: 16,
  },
});
