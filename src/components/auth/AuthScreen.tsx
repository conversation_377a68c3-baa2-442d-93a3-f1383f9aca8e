import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useAuth } from '../../context/AuthContext';
import { useThemedStyles } from '../../context/ThemeContext';
import { ThemedView, ThemedText, ThemedButton, ThemedTextInput } from '../common';

type AuthMode = 'signin' | 'signup' | 'reset';

export default function AuthScreen() {
  const { signIn, signUp, signInWithGoogle, signInWithApple, resetPassword, loading } = useAuth();
  
  const [mode, setMode] = useState<AuthMode>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (isSubmitting || loading) return;

    // Basic validation
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address.');
      return;
    }

    if (mode === 'reset') {
      await handlePasswordReset();
      return;
    }

    if (!password) {
      Alert.alert('Error', 'Please enter your password.');
      return;
    }

    if (mode === 'signup') {
      if (password !== confirmPassword) {
        Alert.alert('Error', 'Passwords do not match.');
        return;
      }
      if (password.length < 6) {
        Alert.alert('Error', 'Password must be at least 6 characters long.');
        return;
      }
    }

    setIsSubmitting(true);

    try {
      if (mode === 'signin') {
        await signIn(email, password);
      } else if (mode === 'signup') {
        await signUp(email, password, fullName);
        Alert.alert(
          'Success',
          'Account created successfully! You can now start generating stories.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert('Error', (error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordReset = async () => {
    setIsSubmitting(true);
    
    try {
      await resetPassword(email);
      Alert.alert(
        'Password Reset',
        'If an account with this email exists, you will receive a password reset link shortly.',
        [
          {
            text: 'OK',
            onPress: () => setMode('signin'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', (error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSocialSignIn = async (provider: 'google' | 'apple') => {
    if (isSubmitting || loading) return;

    setIsSubmitting(true);
    
    try {
      if (provider === 'google') {
        await signInWithGoogle();
      } else {
        await signInWithApple();
      }
    } catch (error) {
      Alert.alert('Error', (error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setConfirmPassword('');
    setFullName('');
  };

  const switchMode = (newMode: AuthMode) => {
    setMode(newMode);
    resetForm();
  };

  const getTitle = () => {
    switch (mode) {
      case 'signin':
        return 'Welcome Back';
      case 'signup':
        return 'Create Account';
      case 'reset':
        return 'Reset Password';
    }
  };

  const getSubtitle = () => {
    switch (mode) {
      case 'signin':
        return 'Sign in to continue your storytelling journey';
      case 'signup':
        return 'Join us to start creating amazing origin stories';
      case 'reset':
        return 'Enter your email to receive a password reset link';
    }
  };

  const getButtonText = () => {
    if (isSubmitting) return 'Please wait...';
    
    switch (mode) {
      case 'signin':
        return 'Sign In';
      case 'signup':
        return 'Create Account';
      case 'reset':
        return 'Send Reset Link';
    }
  };

  const styles = useThemedStyles(createStyles);

  return (
    <ThemedView style={{ flex: 1 }}>
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <StatusBar style="auto" />
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <ThemedView style={styles.content}>
            {/* Header */}
            <ThemedView style={styles.header}>
              <ThemedText variant="title" style={styles.appTitle}>
                Origin Stories
              </ThemedText>
              <ThemedText variant="subtitle" color="textSecondary" style={styles.title}>
                {getTitle()}
              </ThemedText>
              <ThemedText color="textMuted" style={styles.subtitle}>
                {getSubtitle()}
              </ThemedText>
            </ThemedView>

            {/* Form */}
            <ThemedView style={styles.form}>
              {/* Full Name (signup only) */}
              {mode === 'signup' && (
                <ThemedTextInput
                  label="Full Name (Optional)"
                  placeholder="Enter your full name"
                  value={fullName}
                  onChangeText={setFullName}
                  autoCapitalize="words"
                  textContentType="name"
                />
              )}

              {/* Email */}
              <ThemedTextInput
                label="Email Address"
                placeholder="Enter your email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="emailAddress"
              />

              {/* Password (not for reset) */}
              {mode !== 'reset' && (
                <ThemedTextInput
                  label="Password"
                  placeholder="Enter your password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  textContentType={mode === 'signin' ? 'password' : 'newPassword'}
                />
              )}

              {/* Confirm Password (signup only) */}
              {mode === 'signup' && (
                <ThemedTextInput
                  label="Confirm Password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry
                  textContentType="newPassword"
                />
              )}

              {/* Submit Button */}
              <ThemedButton
                title={getButtonText()}
                onPress={handleSubmit}
                disabled={isSubmitting || loading}
                loading={isSubmitting || loading}
                style={styles.submitButton}
              />

              {/* Social Sign In (not for reset) */}
              {mode !== 'reset' && (
                <>
                  <ThemedView style={styles.divider}>
                    <ThemedView style={styles.dividerLine} />
                    <ThemedText color="textMuted" style={styles.dividerText}>or</ThemedText>
                    <ThemedView style={styles.dividerLine} />
                  </ThemedView>

                  <ThemedView style={styles.socialButtons}>
                    {/* Google Sign In */}
                    <ThemedButton
                      title="Continue with Google"
                      variant="secondary"
                      onPress={() => handleSocialSignIn('google')}
                      disabled={isSubmitting || loading}
                    />

                    {/* Apple Sign In (iOS only) */}
                    {Platform.OS === 'ios' && (
                      <ThemedButton
                        title="Continue with Apple"
                        variant="secondary"
                        onPress={() => handleSocialSignIn('apple')}
                        disabled={isSubmitting || loading}
                        style={styles.appleButton}
                      />
                    )}
                  </ThemedView>
                </>
              )}
            </ThemedView>

            {/* Footer Links */}
            <ThemedView style={styles.footer}>
              {mode === 'signin' && (
                <>
                  <ThemedButton
                    title="Forgot your password?"
                    variant="ghost"
                    onPress={() => switchMode('reset')}
                  />
                  
                  <ThemedView style={styles.footerRow}>
                    <ThemedText color="textMuted">
                      Don't have an account?{' '}
                    </ThemedText>
                    <ThemedButton
                      title="Sign up"
                      variant="ghost"
                      onPress={() => switchMode('signup')}
                      style={{ paddingHorizontal: 0 }}
                    />
                  </ThemedView>
                </>
              )}

              {mode === 'signup' && (
                <ThemedView style={styles.footerRow}>
                  <ThemedText color="textMuted">
                    Already have an account?{' '}
                  </ThemedText>
                  <ThemedButton
                    title="Sign in"
                    variant="ghost"
                    onPress={() => switchMode('signin')}
                    style={{ paddingHorizontal: 0 }}
                  />
                </ThemedView>
              )}

              {mode === 'reset' && (
                <ThemedButton
                  title="Back to sign in"
                  variant="ghost"
                  onPress={() => switchMode('signin')}
                />
              )}
            </ThemedView>
          </ThemedView>
        </ScrollView>
      </KeyboardAvoidingView>
    </ThemedView>
  );
}

const createStyles = (colors: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48,
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  appTitle: {
    fontSize: 36,
    marginBottom: 8,
  },
  title: {
    marginBottom: 16,
  },
  subtitle: {
    textAlign: 'center',
  },
  form: {
    marginBottom: 32,
  },
  submitButton: {
    marginBottom: 24,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.divider,
  },
  dividerText: {
    marginHorizontal: 16,
  },
  socialButtons: {
    gap: 12,
  },
  appleButton: {
    backgroundColor: isDark ? '#ffffff' : '#000000',
  },
  footer: {
    alignItems: 'center',
    gap: 16,
  },
  footerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});