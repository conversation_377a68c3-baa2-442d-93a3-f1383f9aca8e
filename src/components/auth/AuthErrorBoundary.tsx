import React, { Component, ReactNode } from 'react';
import { StyleSheet } from 'react-native';
import { ThemedView, ThemedText, ThemedButton } from '../common';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error boundary component specifically for authentication-related errors
 */
export default class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error to your error reporting service
    console.error('Auth Error Boundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <ThemedView style={styles.container}>
          <ThemedView style={styles.content}>
            <ThemedText variant="title" style={styles.title}>
              Something went wrong
            </ThemedText>
            
            <ThemedText color="textSecondary" style={styles.description}>
              We encountered an unexpected error. Please try again.
            </ThemedText>
            
            {__DEV__ && this.state.error && (
              <ThemedView style={styles.errorDetails}>
                <ThemedText variant="caption" color="textMuted" style={styles.errorText}>
                  {this.state.error.message}
                </ThemedText>
              </ThemedView>
            )}
            
            <ThemedButton
              title="Try Again"
              onPress={this.handleRetry}
              style={styles.retryButton}
            />
          </ThemedView>
        </ThemedView>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  content: {
    alignItems: 'center',
    maxWidth: 320,
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    textAlign: 'center',
    marginBottom: 32,
  },
  errorDetails: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
    width: '100%',
  },
  errorText: {
    textAlign: 'center',
    fontFamily: 'monospace',
  },
  retryButton: {
    minWidth: 120,
  },
});