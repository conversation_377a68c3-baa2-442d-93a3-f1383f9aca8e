import React, { ReactNode } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { useAuth } from '../../context/AuthContext';
import { useThemedStyles } from '../../context/ThemeContext';
import { ThemedView, ThemedText } from '../common';
import AuthScreen from './AuthScreen';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * AuthGuard component that protects routes requiring authentication
 * Shows loading spinner while checking auth state
 * Shows AuthScreen if user is not authenticated
 * Renders children if user is authenticated
 */
export default function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { user, loading } = useAuth();
  const styles = useThemedStyles(createStyles);

  // Show loading spinner while checking authentication state
  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={styles.activityIndicatorColor} />
        <ThemedText style={styles.loadingText}>
          Loading...
        </ThemedText>
      </ThemedView>
    );
  }

  // Show custom fallback or AuthScreen if user is not authenticated
  if (!user) {
    return fallback || <AuthScreen />;
  }

  // User is authenticated, render protected content
  return <>{children}</>;
}

interface PublicOnlyGuardProps {
  children: ReactNode;
  redirectTo?: ReactNode;
}

/**
 * PublicOnlyGuard component that protects routes that should only be accessible to unauthenticated users
 * Useful for auth screens that authenticated users shouldn't see
 */
export function PublicOnlyGuard({ children, redirectTo }: PublicOnlyGuardProps) {
  const { user, loading } = useAuth();

  // Show loading spinner while checking authentication state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#92400e" />
        <Text style={styles.loadingText}>
          Loading...
        </Text>
      </View>
    );
  }

  // If user is authenticated, show redirect component or nothing
  if (user) {
    return redirectTo ? <>{redirectTo}</> : null;
  }

  // User is not authenticated, show public content
  return <>{children}</>;
}

interface SubscriptionGuardProps {
  children: ReactNode;
  requiredTier: 'plus' | 'premium' | 'pro';
  fallback?: ReactNode;
}

/**
 * SubscriptionGuard component that protects features requiring specific subscription tiers
 */
export function SubscriptionGuard({ 
  children, 
  requiredTier, 
  fallback 
}: SubscriptionGuardProps) {
  const { user, loading } = useAuth();

  // Show loading spinner while checking authentication state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#92400e" />
        <Text style={styles.loadingText}>
          Loading...
        </Text>
      </View>
    );
  }

  // User must be authenticated first
  if (!user) {
    return <AuthScreen />;
  }

  // Check subscription tier
  const tierHierarchy = {
    free: 0,
    plus: 1,
    premium: 2,
    pro: 3,
  };

  const userTierLevel = tierHierarchy[user.subscription_tier];
  const requiredTierLevel = tierHierarchy[requiredTier];

  if (userTierLevel < requiredTierLevel) {
    return fallback || (
      <View style={styles.restrictedContainer}>
        <Text style={styles.restrictedTitle}>
          Premium Feature
        </Text>
        <Text style={styles.restrictedDescription}>
          This feature requires a {requiredTier} subscription or higher.
        </Text>
        <Text style={styles.restrictedCurrentPlan}>
          Current plan: {user.subscription_tier}
        </Text>
      </View>
    );
  }

  // User has required subscription tier
  return <>{children}</>;
}

interface ConditionalAuthGuardProps {
  children: ReactNode;
  condition: (user: any) => boolean;
  fallback?: ReactNode;
}

/**
 * ConditionalAuthGuard component for custom authentication conditions
 */
export function ConditionalAuthGuard({ 
  children, 
  condition, 
  fallback 
}: ConditionalAuthGuardProps) {
  const { user, loading } = useAuth();

  // Show loading spinner while checking authentication state
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#92400e" />
        <Text style={styles.loadingText}>
          Loading...
        </Text>
      </View>
    );
  }

  // User must be authenticated first
  if (!user) {
    return <AuthScreen />;
  }

  // Check custom condition
  if (!condition(user)) {
    return fallback || (
      <View style={styles.restrictedContainer}>
        <Text style={styles.restrictedTitle}>
          Access Restricted
        </Text>
        <Text style={styles.restrictedDescription}>
          You don't have permission to access this feature.
        </Text>
      </View>
    );
  }

  // Condition is met
  return <>{children}</>;
}

const createStyles = (colors: any, isDark: boolean) => StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    marginTop: 16,
  },
  activityIndicatorColor: colors.primary,
  restrictedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  restrictedTitle: {
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 16,
  },
  restrictedDescription: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  restrictedCurrentPlan: {
    fontSize: 14,
    textAlign: 'center',
  },
});