@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme transition animations */
@layer base {
  * {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }
  
  /* Preserve the elegant serif font styling from original */
  .font-serif {
    font-family: Georgia, 'Times New Roman', serif;
  }
  
  /* Smooth theme transitions */
  .theme-transition {
    transition: all 0.3s ease-in-out;
  }
  
  /* Book-like styling enhancements */
  .book-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .book-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  /* Elegant focus states */
  .elegant-focus:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
  
  /* Theme-specific body styling */
  body.theme-light {
    background-color: #f9fafb;
    color: #111827;
  }
  
  body.theme-dark {
    background-color: #111827;
    color: #f9fafb;
  }
}

/* Custom animations for story generation phases */
@layer utilities {
  .animate-fade-in {
    animation: fade-in 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.4s ease-out;
  }
  
  .animate-pulse-gentle {
    animation: pulse-gentle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse-gentle {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
  
  /* Preserve original amber color scheme */
  .text-amber-custom {
    color: #92400e;
  }
  
  .bg-amber-custom {
    background-color: #92400e;
  }
  
  .border-amber-custom {
    border-color: #92400e;
  }
}