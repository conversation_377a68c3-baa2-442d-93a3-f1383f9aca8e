# Design Document

## Overview

The Origin Stories App will be built as a cross-platform application using React Native with Expo for unified development across web, iOS, and Android. The app maintains the existing elegant, book-like aesthetic while adding comprehensive user management, subscription handling, and enhanced features through Supabase backend integration.

### Design Consistency Reference

**IMPORTANT**: The original artifact code is preserved in `original-artifact.jsx` and MUST be used as the foundation for maintaining design consistency. All new components should follow these established patterns:

- **Typography**: Serif fonts (`font-serif`) for all text elements
- **Color Scheme**: Amber/brown tones (`amber-800`, `amber-700`) with gray scale (`gray-50`, `gray-900`)
- **Layout**: Clean, centered layouts with generous padding (`p-12`, `p-8`)
- **Components**: Rounded corners (`rounded-lg`), subtle shadows (`shadow-sm`)
- **Animations**: Preserve the existing SVG animations for story generation phases
- **Book Aesthetic**: Maintain the book cover design and story presentation format

### Technology Stack

- **Frontend**: React Native with Expo (enables web, iOS, Android from single codebase)
- **Backend**: Supabase (PostgreSQL database, authentication, storage, real-time subscriptions)
- **State Management**: React Context API with useReducer for complex state
- **Navigation**: React Navigation v6 for cross-platform navigation
- **Styling**: NativeWind (Tailwind CSS for React Native) to maintain existing design
- **Text-to-Speech**: Expo Speech API with premium voice integration
- **File Generation**: React-PDF for PDF generation, custom DOCX generation
- **Payments**: Stripe integration through Supabase Edge Functions

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile Apps   │    │   Web App       │    │   Supabase      │
│   (iOS/Android) │    │   (Browser)     │    │   Backend       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ React Native    │    │ React Native    │    │ PostgreSQL DB   │
│ Expo Runtime    │    │ Web Runtime     │    │ Auth Service    │
│                 │    │                 │    │ Storage Service │
│                 │    │                 │    │ Edge Functions  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Shared Core   │
                    │   Components    │
                    │   & Services    │
                    └─────────────────┘
```

### Application Structure

```
src/
├── components/           # Reusable UI components
│   ├── common/          # Cross-platform components
│   ├── story/           # Story-related components
│   └── auth/            # Authentication components
├── screens/             # Main application screens
├── services/            # Business logic and API calls
├── hooks/               # Custom React hooks
├── context/             # React Context providers
├── utils/               # Utility functions
├── constants/           # App constants and configuration
└── types/               # TypeScript type definitions
```

## Components and Interfaces

### Core Components

#### 1. Authentication System

**AuthProvider Context**
```typescript
interface AuthContextType {
  user: User | null;
  session: Session | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  loading: boolean;
}
```

**AuthScreen Component**
- Email/password authentication
- Social login options (Google, Apple)
- Password reset functionality
- Responsive design for all platforms

#### 2. Story Generation System

**StoryProvider Context**
```typescript
interface StoryContextType {
  currentStory: Story | null;
  isGenerating: boolean;
  progress: number;
  generateStory: (topic: string, settings: StorySettings) => Promise<void>;
  saveStory: (story: Story) => Promise<void>;
  settings: StorySettings;
  updateSettings: (settings: Partial<StorySettings>) => void;
}
```

**StorySettings Interface**
```typescript
interface StorySettings {
  ageGroup: 'children' | 'kids' | 'teens' | 'adults' | 'professional';
  tone: 'funny' | 'inspiring' | 'epic' | 'professional' | 'standard';
  voiceEnabled: boolean;
  voiceStyle?: string;
}
```

#### 3. Navigation System

**Main Navigation Structure**
```typescript
type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  History: undefined;
  Settings: undefined;
  Subscription: undefined;
  StoryViewer: { storyId: string };
};
```

#### 4. Theme System

**ThemeProvider Context**
```typescript
interface ThemeContextType {
  isDark: boolean;
  toggleTheme: () => void;
  colors: ColorScheme;
  fonts: FontScheme;
}
```

### User Interface Components

#### 1. MainScreen
- Story input interface (preserving existing design)
- Generation progress with animated SVGs
- Story display in book format
- Navigation icons (history, settings, dark mode)

#### 2. HistoryScreen
- List of previously generated stories
- Search and filter functionality
- Story preview cards
- Delete and share options

#### 3. SettingsScreen
- User profile management
- Subscription status and upgrade options
- Theme toggle
- Terms of Use and Privacy Policy links
- Account deletion option

#### 4. StoryViewerScreen
- Full story display
- Text-to-speech controls
- Download and share options
- Follow-up question interface

## Data Models

### Database Schema (Supabase)

#### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free',
  subscription_expires_at TIMESTAMP,
  story_count_current_month INTEGER DEFAULT 0,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Stories Table
```sql
CREATE TABLE stories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  topic TEXT NOT NULL,
  title TEXT NOT NULL,
  theme TEXT,
  opening TEXT NOT NULL,
  chapters JSONB NOT NULL,
  conclusion TEXT NOT NULL,
  settings JSONB NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### Subscriptions Table
```sql
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  stripe_subscription_id TEXT UNIQUE,
  status TEXT NOT NULL,
  tier TEXT NOT NULL,
  current_period_start TIMESTAMP,
  current_period_end TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### TypeScript Interfaces

#### Story Interface
```typescript
interface Story {
  id: string;
  userId: string;
  topic: string;
  title: string;
  theme: string;
  opening: string;
  chapters: Chapter[];
  conclusion: string;
  settings: StorySettings;
  createdAt: string;
}

interface Chapter {
  title: string;
  text: string;
}
```

#### User Interface
```typescript
interface User {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  subscriptionTier: 'free' | 'plus' | 'premium' | 'pro';
  subscriptionExpiresAt?: string;
  storyCountCurrentMonth: number;
  settings: UserSettings;
}

interface UserSettings {
  defaultAgeGroup: string;
  defaultTone: string;
  voiceEnabled: boolean;
  voiceStyle?: string;
  darkMode: boolean;
}
```

## Error Handling

### Error Categories

1. **Network Errors**: Connection issues, API timeouts
2. **Authentication Errors**: Invalid credentials, expired sessions
3. **Validation Errors**: Invalid input, missing required fields
4. **Subscription Errors**: Usage limits, payment failures
5. **Generation Errors**: AI service failures, content policy violations

### Error Handling Strategy

```typescript
interface AppError {
  type: 'network' | 'auth' | 'validation' | 'subscription' | 'generation';
  message: string;
  code?: string;
  retryable: boolean;
}

class ErrorHandler {
  static handle(error: AppError): void {
    // Log error for monitoring
    // Show user-friendly message
    // Provide retry options if applicable
    // Fallback to offline mode if possible
  }
}
```

### Offline Handling

- Cache recently viewed stories locally
- Queue story generation requests when offline
- Sync data when connection is restored
- Provide clear offline indicators

## Testing Strategy

### Testing Pyramid

#### Unit Tests (70%)
- Component rendering and behavior
- Service functions and API calls
- Utility functions and helpers
- State management logic

#### Integration Tests (20%)
- Screen navigation flows
- Authentication workflows
- Story generation process
- Subscription management

#### End-to-End Tests (10%)
- Complete user journeys
- Cross-platform compatibility
- Performance benchmarks
- Accessibility compliance

### Testing Tools

- **Jest**: Unit and integration testing
- **React Native Testing Library**: Component testing
- **Detox**: End-to-end testing for mobile
- **Playwright**: Web end-to-end testing
- **Maestro**: Cross-platform UI testing

### Test Coverage Goals

- Minimum 80% code coverage
- 100% coverage for critical paths (auth, payments)
- Performance tests for story generation
- Accessibility tests for all screens

## Security Considerations

### Authentication Security
- Secure token storage using Expo SecureStore
- Automatic token refresh
- Biometric authentication support
- Session timeout handling

### Data Protection
- Row Level Security (RLS) policies in Supabase
- Encrypted data transmission (HTTPS/WSS)
- Secure file storage with access controls
- GDPR compliance for user data

### Payment Security
- PCI DSS compliant payment processing
- Secure webhook handling
- Subscription validation
- Fraud prevention measures

## Performance Optimization

### App Performance
- Lazy loading of screens and components
- Image optimization and caching
- Bundle splitting for web platform
- Memory management for long-running processes

### Story Generation Performance
- Streaming responses for real-time progress
- Caching of generated content
- Background processing for non-critical tasks
- Rate limiting and queue management

### Database Performance
- Optimized queries with proper indexing
- Connection pooling
- Caching frequently accessed data
- Pagination for large datasets

## Accessibility

### WCAG 2.1 AA Compliance
- Proper heading hierarchy
- Sufficient color contrast ratios
- Keyboard navigation support
- Screen reader compatibility

### Platform-Specific Accessibility
- iOS VoiceOver support
- Android TalkBack support
- Web keyboard navigation
- Focus management across platforms

### Accessibility Features
- Text size adjustment
- High contrast mode
- Voice control support
- Reduced motion options