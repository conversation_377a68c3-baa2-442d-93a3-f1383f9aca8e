// ORIGINAL ARTIFACT CODE - REFERENCE FOR DESIGN CONSISTENCY
// This file contains the original React component that should be used as the foundation
// for maintaining theme, fonts, colors, and overall aesthetic in the cross-platform app

import React, { useState, useEffect } from 'react';
import { <PERSON>, Feather, Sparkles, ChevronRight } from 'lucide-react';

const TRANSLATIONS = {
  "en-US": {
    "originStories": "Origin Stories",
    "legendaryBeginnings": "Discover the legendary beginnings of anything your heart desires",
    "whatOriginStory": "What origin story shall we tell?",
    "enterAnything": "Enter anything - an object, concept, tradition, or invention",
    "placeholderText": "e.g., coffee, the internet, democracy, pizza...",
    "begin": "Begin",
    "generating": "Generating...",
    "doingDigging": "Doing my digging",
    "planningStructure": "Planning the narrative structure...",
    "craftingOpening": "Crafting the opening...",
    "buildingNarrative": "Building the narrative...",
    "craftingRevelation": "Crafting the revelation...",
    "bindingPages": "Binding the pages...",
    "complete": "Complete!",
    "errorOccurred": "An error occurred while crafting your story. Please try again.",
    "theRevelation": "The Revelation",
    "furtherExploration": "Further Exploration",
    "wantDigDeeper": "Want to dig deeper?",
    "askAboutDetails": "Ask about specific details, connections, or implications...",
    "researching": "Researching...",
    "ask": "Ask",
    "discoverAnother": "Discover another origin",
    "followUpError": "I apologize, but I encountered an error while researching your question. Please try asking again.",
    "claudePromptSuffix": "Please respond in ${locale} language"
  },
  "es-ES": {
    "originStories": "Historias de Origen",
    "legendaryBeginnings": "Descubre los comienzos legendarios de cualquier cosa que tu corazón desee",
    "whatOriginStory": "¿Qué historia de origen contaremos?",
    "enterAnything": "Ingresa cualquier cosa - un objeto, concepto, tradición o invención",
    "placeholderText": "ej., café, internet, democracia, pizza...",
    "begin": "Comenzar",
    "generating": "Generando...",
    "doingDigging": "Investigando",
    "planningStructure": "Planificando la estructura narrativa...",
    "craftingOpening": "Creando la apertura...",
    "buildingNarrative": "Construyendo la narrativa...",
    "craftingRevelation": "Creando la revelación...",
    "bindingPages": "Encuadernando las páginas...",
    "complete": "¡Completo!",
    "errorOccurred": "Ocurrió un error al crear tu historia. Por favor, inténtalo de nuevo.",
    "theRevelation": "La Revelación",
    "furtherExploration": "Exploración Adicional",
    "wantDigDeeper": "¿Quieres profundizar más?",
    "askAboutDetails": "Pregunta sobre detalles específicos, conexiones o implicaciones...",
    "researching": "Investigando...",
    "ask": "Preguntar",
    "discoverAnother": "Descubrir otro origen",
    "followUpError": "Me disculpo, pero encontré un error al investigar tu pregunta. Por favor, intenta preguntar de nuevo.",
    "claudePromptSuffix": "Por favor responde en idioma ${locale}"
  }
};

const appLocale = '{{APP_LOCALE}}';
const browserLocale = navigator.languages?.[0] || navigator.language || 'en-US';

const findMatchingLocale = (locale) => {
  if (TRANSLATIONS[locale]) return locale;
  const lang = locale.split('-')[0];
  const match = Object.keys(TRANSLATIONS).find(key => key.startsWith(lang + '-'));
  return match || 'en-US';
};

const locale = (appLocale !== '{{APP_LOCALE}}') ? findMatchingLocale(appLocale) : findMatchingLocale(browserLocale);
const t = (key) => TRANSLATIONS[locale]?.[key] || TRANSLATIONS['en-US'][key] || key;

const OriginStoryBook = () => {
  const [topic, setTopic] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [story, setStory] = useState(null);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);
  const [currentPhase, setCurrentPhase] = useState('planning'); // planning, opening, chapters, conclusion, binding
  const [followUpQuestion, setFollowUpQuestion] = useState('');
  const [followUpAnswer, setFollowUpAnswer] = useState('');
  const [isAnsweringFollowUp, setIsAnsweringFollowUp] = useState(false);

  const generateOriginStory = async () => {
    if (!topic.trim()) return;
    
    setIsGenerating(true);
    setStory(null);
    setProgress(0);
    
    try {
      // Step 1: Plan the story structure
      setCurrentPhase('planning');
      setCurrentStep(t('planningStructure'));
      setProgress(20);
      
      const planPrompt = `You are a master storyteller planning an origin story for "${topic}".

Create a compelling narrative structure for this origin story. Consider:
- The mysterious or legendary beginnings
- Key moments of transformation or discovery
- Cultural, historical, or mythical context
- Emotional arc and themes
- Memorable characters or forces involved

Respond ONLY with a valid JSON object in this exact format:
{
  "title": "An evocative title for the origin story",
  "theme": "The central theme or message",
  "structure": [
    {
      "chapter": 1,
      "title": "Chapter title",
      "focus": "What this chapter should cover",
      "tone": "The emotional tone for this part"
    },
    {
      "chapter": 2,
      "title": "Chapter title", 
      "focus": "What this chapter should cover",
      "tone": "The emotional tone for this part"
    },
    {
      "chapter": 3,
      "title": "Chapter title",
      "focus": "What this chapter should cover", 
      "tone": "The emotional tone for this part"
    }
  ],
  "keyElements": ["important element 1", "important element 2", "important element 3"]
}

${t('claudePromptSuffix').replace('${locale}', locale)}

DO NOT OUTPUT ANYTHING OTHER THAN VALID JSON.`;

      const planResponse = await window.claude.complete(planPrompt);
      const plan = JSON.parse(planResponse);

      // Step 2: Generate opening
      setCurrentPhase('opening');
      setCurrentStep(t('craftingOpening'));
      setProgress(40);
      
      const openingPrompt = `You are writing the opening of an origin story for "${topic}".

STORY PLAN:
${JSON.stringify(plan, null, 2)}

Write a captivating opening that sets the scene and draws readers into the origin story. This should be the very beginning - establish the world, atmosphere, and hint at the mystery to come. Write in a literary, engaging style that feels timeless and mythic.

Write approximately 150-200 words. Focus on creating atmosphere and intrigue.

${t('claudePromptSuffix').replace('${locale}', locale)}

Respond ONLY with a valid JSON object:
{
  "text": "The opening text here",
  "summary": "Brief summary of what happened in this opening"
}

DO NOT OUTPUT ANYTHING OTHER THAN VALID JSON.`;

      const openingResponse = await window.claude.complete(openingPrompt);
      const opening = JSON.parse(openingResponse);

      // Step 3: Generate middle chapters
      setCurrentPhase('chapters');
      setCurrentStep(t('buildingNarrative'));
      setProgress(60);
      
      const chapters = [];
      let storyContext = opening.summary;
      
      for (let i = 0; i < plan.structure.length; i++) {
        const chapter = plan.structure[i];
        
        const chapterPrompt = `You are continuing an origin story for "${topic}".

COMPLETE STORY CONTEXT:
Story Plan: ${JSON.stringify(plan, null, 2)}
Previous Story Events: ${storyContext}
Current Chapter to Write: ${JSON.stringify(chapter, null, 2)}

Write this chapter of the origin story. It should flow naturally from what came before and advance the narrative toward the origin revelation. Maintain the literary, mythic tone established in the opening.

Write approximately 150-200 words for this chapter.

${t('claudePromptSuffix').replace('${locale}', locale)}

Respond ONLY with a valid JSON object:
{
  "text": "The chapter text here",
  "summary": "Brief summary of what happened in this chapter"
}

DO NOT OUTPUT ANYTHING OTHER THAN VALID JSON.`;

        const chapterResponse = await window.claude.complete(chapterPrompt);
        const chapterData = JSON.parse(chapterResponse);
        
        chapters.push({
          title: chapter.title,
          text: chapterData.text
        });
        
        // Update context for next chapter
        storyContext += ` ${chapterData.summary}`;
      }

      // Step 4: Generate conclusion
      setCurrentPhase('conclusion');
      setCurrentStep(t('craftingRevelation'));
      setProgress(80);
      
      const conclusionPrompt = `You are writing the powerful conclusion to an origin story for "${topic}".

COMPLETE STORY CONTEXT:
Story Plan: ${JSON.stringify(plan, null, 2)}
Full Story Events So Far: ${storyContext}

Write a satisfying conclusion that reveals the true origin and brings the story full circle. This should be the climactic moment where everything comes together - the revelation of how "${topic}" truly came to be. Make it memorable and emotionally resonant.

Write approximately 150-200 words.

${t('claudePromptSuffix').replace('${locale}', locale)}

Respond ONLY with a valid JSON object:
{
  "text": "The conclusion text here"
}

DO NOT OUTPUT ANYTHING OTHER THAN VALID JSON.`;

      const conclusionResponse = await window.claude.complete(conclusionPrompt);
      const conclusion = JSON.parse(conclusionResponse);

      // Assemble final story
      setCurrentPhase('binding');
      setCurrentStep(t('bindingPages'));
      setProgress(100);
      
      const finalStory = {
        title: plan.title,
        theme: plan.theme,
        opening: opening.text,
        chapters: chapters,
        conclusion: conclusion.text
      };
      
      setStory(finalStory);
      setCurrentStep(t('complete'));
      
    } catch (error) {
      console.error('Error generating story:', error);
      setCurrentStep(t('errorOccurred'));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleFollowUpQuestion = async () => {
    if (!followUpQuestion.trim() || !story) return;
    
    setIsAnsweringFollowUp(true);
    
    try {
      const followUpPrompt = `You are an expert researcher responding to a follow-up question about an origin story.

ORIGINAL STORY CONTEXT:
Title: ${story.title}
Theme: ${story.theme}
Full Story Content: 
Opening: ${story.opening}
${story.chapters.map((ch, i) => `Chapter ${i + 1} - ${ch.title}: ${ch.text}`).join('\n')}
Conclusion: ${story.conclusion}

FOLLOW-UP QUESTION: "${followUpQuestion}"

Provide a thoughtful, detailed response to this question based on the story context and your knowledge of the subject. Write in an engaging, informative tone that matches the storytelling style of the original piece.

Write approximately 100-150 words.

${t('claudePromptSuffix').replace('${locale}', locale)}

Respond ONLY with a valid JSON object:
{
  "answer": "Your detailed response here"
}

DO NOT OUTPUT ANYTHING OTHER THAN VALID JSON.`;

      const response = await window.claude.complete(followUpPrompt);
      const parsed = JSON.parse(response);
      setFollowUpAnswer(parsed.answer);
      
    } catch (error) {
      console.error('Error answering follow-up:', error);
      setFollowUpAnswer(t('followUpError'));
    } finally {
      setIsAnsweringFollowUp(false);
    }
  };

  const renderAnimatedSVG = () => {
    switch (currentPhase) {
      case 'planning':
        return (
          <svg width="120" height="120" viewBox="0 0 120 120" className="mx-auto mb-4">
            <defs>
              <radialGradient id="inkGradient" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor="#6b7280" />
                <stop offset="100%" stopColor="#374151" />
              </radialGradient>
            </defs>
            {/* Quill */}
            <g transform="translate(60, 60)">
              <path 
                d="M-30,-20 Q-20,-25 -10,-20 Q0,-15 10,-10 Q20,-5 30,0 Q35,2 40,5"
                stroke="#6b7280" 
                strokeWidth="3" 
                fill="none"
                strokeLinecap="round"
              >
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  values="0;5;0;-5;0"
                  dur="3s"
                  repeatCount="indefinite"
                />
              </path>
              {/* Feather details */}
              <path 
                d="M-30,-20 Q-25,-15 -20,-18 Q-15,-12 -10,-15"
                stroke="#9ca3af" 
                strokeWidth="1.5" 
                fill="none"
              >
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  values="0;5;0;-5;0"
                  dur="3s"
                  repeatCount="indefinite"
                />
              </path>
              {/* Ink drops */}
              <circle cx="35" cy="5" r="2" fill="url(#inkGradient)">
                <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" />
              </circle>
              {/* Scrolling text lines */}
              <g opacity="0.6">
                <line x1="-40" y1="20" x2="20" y2="20" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="x2" values="20;40;20" dur="4s" repeatCount="indefinite" />
                </line>
                <line x1="-40" y1="30" x2="15" y2="30" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="x2" values="15;35;15" dur="4.5s" repeatCount="indefinite" />
                </line>
                <line x1="-40" y1="40" x2="25" y2="40" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="x2" values="25;45;25" dur="3.5s" repeatCount="indefinite" />
                </line>
              </g>
            </g>
          </svg>
        );
        
      case 'opening':
        return (
          <svg width="120" height="120" viewBox="0 0 120 120" className="mx-auto mb-4">
            <defs>
              <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#a16207" />
                <stop offset="100%" stopColor="#92400e" />
              </linearGradient>
            </defs>
            {/* Book opening animation */}
            <g transform="translate(60, 60)">
              {/* Left page */}
              <rect 
                x="-40" y="-30" width="35" height="60" 
                fill="url(#bookGradient)" 
                stroke="#6b7280" 
                strokeWidth="2"
                rx="3"
              >
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  values="0;-20;0"
                  dur="3s"
                  repeatCount="indefinite"
                />
              </rect>
              {/* Right page */}
              <rect 
                x="5" y="-30" width="35" height="60" 
                fill="url(#bookGradient)" 
                stroke="#6b7280" 
                strokeWidth="2"
                rx="3"
              >
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  values="0;20;0"
                  dur="3s"
                  repeatCount="indefinite"
                />
              </rect>
              {/* Magical sparkles */}
              <circle cx="-20" cy="-40" r="2" fill="#9ca3af">
                <animate attributeName="opacity" values="0;1;0" dur="1.5s" repeatCount="indefinite" />
                <animate attributeName="cy" values="-40;-50;-40" dur="2s" repeatCount="indefinite" />
              </circle>
              <circle cx="0" cy="-45" r="1.5" fill="#6b7280">
                <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0.5s" />
                <animate attributeName="cy" values="-45;-55;-45" dur="2.5s" repeatCount="indefinite" begin="0.5s" />
              </circle>
              <circle cx="20" cy="-38" r="2.5" fill="#a16207">
                <animate attributeName="opacity" values="0;1;0" dur="1.8s" repeatCount="indefinite" begin="1s" />
                <animate attributeName="cy" values="-38;-48;-38" dur="2.2s" repeatCount="indefinite" begin="1s" />
              </circle>
            </g>
          </svg>
        );
        
      case 'chapters':
        return (
          <svg width="120" height="120" viewBox="0 0 120 120" className="mx-auto mb-4">
            <defs>
              <linearGradient id="pageGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#f9fafb" />
                <stop offset="100%" stopColor="#e5e7eb" />
              </linearGradient>
            </defs>
            {/* Pages turning */}
            <g transform="translate(60, 60)">
              {/* Base book */}
              <rect x="-35" y="-25" width="70" height="50" fill="#6b7280" rx="3" />
              {/* Turning pages */}
              <g>
                <rect 
                  x="-30" y="-20" width="60" height="40" 
                  fill="url(#pageGradient)" 
                  stroke="#9ca3af" 
                  strokeWidth="1"
                  rx="2"
                >
                  <animateTransform
                    attributeName="transform"
                    type="rotateY"
                    values="0;180;0"
                    dur="2s"
                    repeatCount="indefinite"
                  />
                </rect>
                <rect 
                  x="-30" y="-20" width="60" height="40" 
                  fill="url(#pageGradient)" 
                  stroke="#9ca3af" 
                  strokeWidth="1"
                  rx="2"
                  opacity="0.7"
                >
                  <animateTransform
                    attributeName="transform"
                    type="rotateY"
                    values="0;180;0"
                    dur="2s"
                    repeatCount="indefinite"
                    begin="0.3s"
                  />
                </rect>
                <rect 
                  x="-30" y="-20" width="60" height="40" 
                  fill="url(#pageGradient)" 
                  stroke="#9ca3af" 
                  strokeWidth="1"
                  rx="2"
                  opacity="0.4"
                >
                  <animateTransform
                    attributeName="transform"
                    type="rotateY"
                    values="0;180;0"
                    dur="2s"
                    repeatCount="indefinite"
                    begin="0.6s"
                  />
                </rect>
              </g>
              {/* Text lines appearing */}
              <g opacity="0.8">
                <line x1="-20" y1="-10" x2="20" y2="-10" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="stroke-dasharray" values="0,40;40,0" dur="1.5s" repeatCount="indefinite" />
                </line>
                <line x1="-20" y1="-5" x2="15" y2="-5" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="stroke-dasharray" values="0,35;35,0" dur="1.8s" repeatCount="indefinite" />
                </line>
                <line x1="-20" y1="0" x2="25" y2="0" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="stroke-dasharray" values="0,45;45,0" dur="1.3s" repeatCount="indefinite" />
                </line>
                <line x1="-20" y1="5" x2="18" y2="5" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="stroke-dasharray" values="0,38;38,0" dur="1.6s" repeatCount="indefinite" />
                </line>
                <line x1="-20" y1="10" x2="22" y2="10" stroke="#6b7280" strokeWidth="1">
                  <animate attributeName="stroke-dasharray" values="0,42;42,0" dur="1.4s" repeatCount="indefinite" />
                </line>
              </g>
            </g>
          </svg>
        );
        
      case 'conclusion':
        return (
          <svg width="120" height="120" viewBox="0 0 120 120" className="mx-auto mb-4">
            <defs>
              <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor="#d1d5db" />
                <stop offset="70%" stopColor="#9ca3af" />
                <stop offset="100%" stopColor="#6b7280" />
              </radialGradient>
            </defs>
            {/* Climactic revelation */}
            <g transform="translate(60, 60)">
              {/* Central orb of knowledge */}
              <circle cx="0" cy="0" r="25" fill="url(#glowGradient)" opacity="0.8">
                <animate attributeName="r" values="20;30;20" dur="2s" repeatCount="indefinite" />
                <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite" />
              </circle>
              {/* Radiating lines */}
              <g stroke="#9ca3af" strokeWidth="2" opacity="0.9">
                <line x1="0" y1="-40" x2="0" y2="-25">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" />
                </line>
                <line x1="28" y1="-28" x2="20" y2="-20">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" begin="0.2s" />
                </line>
                <line x1="40" y1="0" x2="25" y2="0">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" begin="0.4s" />
                </line>
                <line x1="28" y1="28" x2="20" y2="20">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" begin="0.6s" />
                </line>
                <line x1="0" y1="40" x2="0" y2="25">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" begin="0.8s" />
                </line>
                <line x1="-28" y1="28" x2="-20" y2="20">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" begin="1s" />
                </line>
                <line x1="-40" y1="0" x2="-25" y2="0">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" begin="1.2s" />
                </line>
                <line x1="-28" y1="-28" x2="-20" y2="-20">
                  <animate attributeName="stroke-width" values="1;3;1" dur="1.5s" repeatCount="indefinite" begin="1.4s" />
                </line>
              </g>
              {/* Floating symbols */}
              <text x="0" y="5" textAnchor="middle" fontSize="16" fill="#6b7280" fontWeight="bold">
                <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite" />
                ✦
              </text>
            </g>
          </svg>
        );
        
      case 'binding':
        return (
          <svg width="120" height="120" viewBox="0 0 120 120" className="mx-auto mb-4">
            <defs>
              <linearGradient id="leatherGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#6b7280" />
                <stop offset="50%" stopColor="#4b5563" />
                <stop offset="100%" stopColor="#374151" />
              </linearGradient>
            </defs>
            {/* Complete book */}
            <g transform="translate(60, 60)">
              {/* Book spine and cover */}
              <rect 
                x="-30" y="-35" width="60" height="70" 
                fill="url(#leatherGradient)" 
                stroke="#374151" 
                strokeWidth="2"
                rx="5"
              >
                <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" />
              </rect>
              {/* Decorative elements */}
              <rect 
                x="-25" y="-30" width="50" height="60" 
                fill="none" 
                stroke="#a16207" 
                strokeWidth="1"
                rx="3"
              />
              {/* Golden clasps */}
              <circle cx="-20" cy="0" r="3" fill="#a16207">
                <animate attributeName="fill" values="#a16207;#92400e;#a16207" dur="1.5s" repeatCount="indefinite" />
              </circle>
              <circle cx="20" cy="0" r="3" fill="#a16207">
                <animate attributeName="fill" values="#a16207;#92400e;#a16207" dur="1.5s" repeatCount="indefinite" begin="0.5s" />
              </circle>
              {/* Title plate */}
              <rect x="-15" y="-15" width="30" height="30" fill="#f3f4f6" stroke="#9ca3af" strokeWidth="1" rx="2" />
              {/* Completion sparkles */}
              <circle cx="-35" cy="-40" r="2" fill="#9ca3af">
                <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" />
              </circle>
              <circle cx="35" cy="-40" r="2" fill="#6b7280">
                <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0.3s" />
              </circle>
              <circle cx="-35" cy="40" r="2" fill="#a1a1aa">
                <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0.6s" />
              </circle>
              <circle cx="35" cy="40" r="2" fill="#9ca3af">
                <animate attributeName="opacity" values="0;1;0" dur="1s" repeatCount="indefinite" begin="0.9s" />
              </circle>
            </g>
          </svg>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12 pt-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Book className="w-6 h-6 text-amber-800" />
            <h1 className="text-5xl font-serif text-gray-900 font-normal">
              {t('originStories')}
            </h1>
            <Feather className="w-6 h-6 text-amber-800" />
          </div>
          <p className="text-gray-700 text-xl font-serif italic">
            {t('legendaryBeginnings')}
          </p>
        </div>

        {/* Input Section */}
        {!story && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 mb-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-serif text-gray-900 mb-4">
                {t('whatOriginStory')}
              </h2>
              <p className="text-gray-600 font-serif">
                {t('enterAnything')}
              </p>
            </div>
            
            <div className="flex gap-4">
              <input
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                placeholder={t('placeholderText')}
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg font-serif"
                onKeyPress={(e) => e.key === 'Enter' && !isGenerating && generateOriginStory()}
                disabled={isGenerating}
              />
              <button
                onClick={generateOriginStory}
                disabled={isGenerating || !topic.trim()}
                className="px-8 py-3 bg-gray-800 hover:bg-gray-900 disabled:bg-gray-400 text-white rounded-lg font-serif text-lg transition-all duration-200 flex items-center gap-2"
              >
                {isGenerating ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    {t('generating')}
                  </>
                ) : (
                  <>
                    {t('begin')}
                    <ChevronRight className="w-5 h-5" />
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Progress with Animated SVGs */}
        {isGenerating && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 mb-8">
            <div className="text-center mb-8">
              {renderAnimatedSVG()}
              <h3 className="text-xl font-serif text-gray-900 mb-2">
                {t('doingDigging')}
              </h3>
              <p className="text-gray-600 font-serif">{currentStep}</p>
            </div>
            
            <div className="w-full bg-gray-200 rounded h-2">
              <div 
                className="bg-blue-500 h-2 rounded transition-all duration-500"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        )}

        {/* Story Display */}
        {story && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Book Cover */}
            <div className="bg-amber-800 p-12 text-center text-white">
              <h1 className="text-4xl font-serif font-normal mb-4 leading-tight">
                {story.title}
              </h1>
              <p className="text-xl font-serif italic opacity-90">
                {story.theme}
              </p>
              <div className="mt-8 flex justify-center">
                <Book className="w-12 h-12 opacity-80" />
              </div>
            </div>

            {/* Story Content */}
            <div className="p-12 space-y-12">
              {/* Opening */}
              <div className="prose prose-lg max-w-none">
                <div className="text-6xl float-left font-serif text-amber-700 leading-none mr-3 mt-1">
                  {story.opening.charAt(0)}
                </div>
                <div className="text-gray-900 font-serif leading-relaxed text-lg">
                  {story.opening.substring(1)}
                </div>
              </div>

              {/* Chapters */}
              {story.chapters.map((chapter, index) => (
                <div key={index} className="border-t border-gray-200 pt-12">
                  <h3 className="text-2xl font-serif text-gray-900 mb-6 text-center">
                    {chapter.title}
                  </h3>
                  <div className="prose prose-lg max-w-none">
                    <p className="text-gray-900 font-serif leading-relaxed text-lg">
                      {chapter.text}
                    </p>
                  </div>
                </div>
              ))}

              {/* Conclusion */}
              <div className="border-t border-gray-200 pt-12">
                <h3 className="text-2xl font-serif text-gray-900 mb-6 text-center">
                  {t('theRevelation')}
                </h3>
                <div className="prose prose-lg max-w-none">
                  <p className="text-gray-900 font-serif leading-relaxed text-lg">
                    {story.conclusion}
                  </p>
                </div>
              </div>
            </div>

            {/* Follow-up Answer */}
            {followUpAnswer && (
              <div className="border-t border-gray-200 pt-12 mx-12">
                <h3 className="text-2xl font-serif text-gray-900 mb-6 text-center">
                  {t('furtherExploration')}
                </h3>
                <div className="prose prose-lg max-w-none">
                  <p className="text-gray-900 font-serif leading-relaxed text-lg">
                    {followUpAnswer}
                  </p>
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="bg-gray-50 p-8 border-t border-gray-200">
              {/* Follow-up Questions */}
              <div className="mb-6">
                <h4 className="text-lg font-serif text-gray-900 mb-4 text-center">
                  {t('wantDigDeeper')}
                </h4>
                <div className="flex gap-3">
                  <input
                    type="text"
                    value={followUpQuestion}
                    onChange={(e) => setFollowUpQuestion(e.target.value)}
                    placeholder={t('askAboutDetails')}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-serif"
                    onKeyPress={(e) => e.key === 'Enter' && !isAnsweringFollowUp && handleFollowUpQuestion()}
                    disabled={isAnsweringFollowUp}
                  />
                  <button
                    onClick={handleFollowUpQuestion}
                    disabled={isAnsweringFollowUp || !followUpQuestion.trim()}
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-serif transition-all duration-200"
                  >
                    {isAnsweringFollowUp ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin inline-block mr-2" />
                        {t('researching')}
                      </>
                    ) : (
                      t('ask')
                    )}
                  </button>
                </div>
              </div>

              <div className="text-center">
                <button
                  onClick={() => {
                    setStory(null); 
                    setTopic(''); 
                    setProgress(0); 
                    setFollowUpQuestion(''); 
                    setFollowUpAnswer('');
                  }}
                  className="px-8 py-3 bg-gray-800 hover:bg-gray-900 text-white rounded-lg font-serif text-lg transition-all duration-200"
                >
                  {t('discoverAnother')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OriginStoryBook;